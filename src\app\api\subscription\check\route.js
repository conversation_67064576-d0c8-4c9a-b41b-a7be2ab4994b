import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// 检查所有用户的订阅状态
export async function GET() {
  try {
    const now = new Date();
    
    // 查找即将过期的订阅（7天内过期）
    const soonToExpire = await prisma.subscription.findMany({
      where: {
        status: 'active',
        endDate: {
          gte: now,
          lte: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000) // 7天后
        }
      },
      include: {
        user: true
      }
    });

    // 查找已过期的订阅
    const expired = await prisma.subscription.findMany({
      where: {
        status: 'active',
        endDate: {
          lt: now
        }
      },
      include: {
        user: true
      }
    });

    // 更新已过期的订阅状态
    if (expired.length > 0) {
      await prisma.subscription.updateMany({
        where: {
          id: {
            in: expired.map(sub => sub.id)
          }
        },
        data: {
          status: 'expired'
        }
      });
    }

    return NextResponse.json({
      message: '订阅状态检查完成',
      data: {
        soonToExpire: soonToExpire.length,
        expired: expired.length,
        expiredSubscriptions: expired.map(sub => ({
          userId: sub.userId,
          userEmail: sub.user.email,
          planType: sub.planType,
          endDate: sub.endDate
        })),
        soonToExpireSubscriptions: soonToExpire.map(sub => ({
          userId: sub.userId,
          userEmail: sub.user.email,
          planType: sub.planType,
          endDate: sub.endDate,
          daysRemaining: Math.ceil((new Date(sub.endDate) - now) / (1000 * 60 * 60 * 24))
        }))
      }
    });

  } catch (error) {
    console.error('订阅状态检查错误:', error);
    return NextResponse.json(
      { error: '订阅状态检查失败' },
      { status: 500 }
    );
  }
}

// 发送续费提醒通知
export async function POST() {
  try {
    const now = new Date();
    
    // 查找即将过期的订阅（3天内过期）
    const soonToExpire = await prisma.subscription.findMany({
      where: {
        status: 'active',
        endDate: {
          gte: now,
          lte: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000) // 3天后
        }
      },
      include: {
        user: true
      }
    });

    // 这里可以发送邮件或其他通知
    // 目前只是记录日志
    console.log(`发现 ${soonToExpire.length} 个即将过期的订阅`);
    
    for (const subscription of soonToExpire) {
      const daysRemaining = Math.ceil((new Date(subscription.endDate) - now) / (1000 * 60 * 60 * 24));
      console.log(`用户 ${subscription.user.email} 的订阅将在 ${daysRemaining} 天后过期`);
      
      // 在实际项目中，这里可以发送邮件通知
      // await sendRenewalReminderEmail(subscription.user.email, daysRemaining);
    }

    return NextResponse.json({
      message: '续费提醒发送完成',
      data: {
        notificationsSent: soonToExpire.length,
        users: soonToExpire.map(sub => ({
          email: sub.user.email,
          daysRemaining: Math.ceil((new Date(sub.endDate) - now) / (1000 * 60 * 60 * 24))
        }))
      }
    });

  } catch (error) {
    console.error('发送续费提醒错误:', error);
    return NextResponse.json(
      { error: '发送续费提醒失败' },
      { status: 500 }
    );
  }
}
