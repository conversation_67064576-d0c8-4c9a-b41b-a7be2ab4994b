import { NextResponse } from 'next/server';
import { CacheService } from '@/lib/cache-service';
import { Scheduler } from '@/lib/scheduler';

/**
 * 缓存管理API
 * GET /api/cache - 获取缓存状态
 * POST /api/cache - 手动刷新缓存
 * DELETE /api/cache - 清理过期缓存
 */

// 获取缓存状态
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'status') {
      // 获取缓存状态
      const cacheStatus = await CacheService.getCacheStatus();
      const schedulerStatus = Scheduler.getStatus();
      const recentTasks = await CacheService.getRecentCacheTasks(5);

      return NextResponse.json({
        code: 0,
        msg: '获取缓存状态成功',
        timestamp: Date.now(),
        data: {
          cacheStatus: cacheStatus,
          schedulerStatus: schedulerStatus,
          recentTasks: recentTasks
        }
      });
    }

    if (action === 'tasks') {
      // 获取缓存任务历史
      const limit = parseInt(searchParams.get('limit')) || 20;
      const tasks = await CacheService.getRecentCacheTasks(limit);

      return NextResponse.json({
        code: 0,
        msg: '获取任务历史成功',
        timestamp: Date.now(),
        data: {
          tasks: tasks,
          total: tasks.length
        }
      });
    }

    // 默认返回简单状态
    const cacheStatus = await CacheService.getCacheStatus();
    return NextResponse.json({
      code: 0,
      msg: '获取缓存状态成功',
      timestamp: Date.now(),
      data: {
        cacheStatus: cacheStatus
      }
    });

  } catch (error) {
    console.error('获取缓存状态失败:', error);
    return NextResponse.json({
      code: -1,
      msg: '获取缓存状态失败: ' + error.message,
      timestamp: Date.now(),
      data: null
    }, { status: 500 });
  }
}

// 手动刷新缓存
export async function POST(request) {
  try {
    const body = await request.json();
    const { tier, action } = body;

    if (action === 'refresh') {
      // 手动刷新缓存
      const result = await Scheduler.executeManualUpdate(tier);
      
      return NextResponse.json({
        code: 0,
        msg: `缓存刷新成功`,
        timestamp: Date.now(),
        data: {
          tier: tier || 'all',
          taskId: result.taskId,
          message: tier ? `${tier} 等级缓存已刷新` : '所有等级缓存已刷新'
        }
      });
    }

    if (action === 'start-scheduler') {
      // 启动定时任务
      Scheduler.start();
      
      return NextResponse.json({
        code: 0,
        msg: '定时任务已启动',
        timestamp: Date.now(),
        data: {
          schedulerStatus: Scheduler.getStatus()
        }
      });
    }

    if (action === 'stop-scheduler') {
      // 停止定时任务
      Scheduler.stop();
      
      return NextResponse.json({
        code: 0,
        msg: '定时任务已停止',
        timestamp: Date.now(),
        data: {
          schedulerStatus: Scheduler.getStatus()
        }
      });
    }

    return NextResponse.json({
      code: -1,
      msg: '不支持的操作',
      timestamp: Date.now(),
      data: null
    }, { status: 400 });

  } catch (error) {
    console.error('缓存操作失败:', error);
    return NextResponse.json({
      code: -1,
      msg: '缓存操作失败: ' + error.message,
      timestamp: Date.now(),
      data: null
    }, { status: 500 });
  }
}

// 清理过期缓存
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'expired') {
      // 清理过期缓存
      const cleanedCount = await CacheService.cleanExpiredCache();
      
      return NextResponse.json({
        code: 0,
        msg: '过期缓存清理完成',
        timestamp: Date.now(),
        data: {
          cleanedCount: cleanedCount,
          message: `已清理 ${cleanedCount} 条过期缓存`
        }
      });
    }

    if (action === 'all') {
      // 清理所有缓存（需要管理员权限）
      // 这里可以添加权限检查
      
      return NextResponse.json({
        code: -1,
        msg: '暂不支持清理所有缓存',
        timestamp: Date.now(),
        data: null
      }, { status: 403 });
    }

    return NextResponse.json({
      code: -1,
      msg: '不支持的清理操作',
      timestamp: Date.now(),
      data: null
    }, { status: 400 });

  } catch (error) {
    console.error('清理缓存失败:', error);
    return NextResponse.json({
      code: -1,
      msg: '清理缓存失败: ' + error.message,
      timestamp: Date.now(),
      data: null
    }, { status: 500 });
  }
}
