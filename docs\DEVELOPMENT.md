# 🛠️ 开发指南

饰品监控助手的开发环境配置和开发规范。

## 🚀 快速开始

### 环境要求
- **Node.js**: 18.x 或更高版本
- **npm**: 8.x 或更高版本
- **Git**: 最新版本

### 一键启动开发环境
```bash
# 克隆项目
git clone https://github.com/your-username/youpin-sentinel.git
cd youpin-sentinel

# 一键部署开发环境
./scripts/deploy.sh local

# 启动开发服务器
npm run dev
```

## 📋 开发命令

### 基础命令
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 修复代码格式
npm run lint:fix

# 类型检查
npm run type-check
```

### 数据库命令
```bash
# 生成 Prisma 客户端
npm run db:generate

# 推送数据库模式
npm run db:push

# 打开数据库管理界面
npm run db:studio

# 重置数据库
npm run db:reset

# 创建超级管理员
npm run db:seed
```

### 部署命令
```bash
# 本地部署
npm run deploy:local

# 生产部署
npm run deploy:prod
```

### PM2 管理命令
```bash
# 启动 PM2 进程
npm run pm2:start

# 停止 PM2 进程
npm run pm2:stop

# 重启 PM2 进程
npm run pm2:restart

# 查看 PM2 日志
npm run pm2:logs

# 查看 PM2 状态
npm run pm2:status
```

### 系统命令
```bash
# 刷新缓存
npm run cache:refresh

# 健康检查
npm run health
```

## 🏗️ 开发流程

### 1. 功能开发流程
1. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **开发功能**
   - 编写代码
   - 添加测试
   - 更新文档

3. **测试功能**
   ```bash
   npm run lint
   npm run type-check
   npm run test
   ```

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   git push origin feature/new-feature
   ```

### 2. API 开发规范

**文件结构：**
```
src/app/api/
└── feature-name/
    ├── route.js           # GET, POST 等方法
    └── [id]/
        └── route.js       # 动态路由
```

**API 响应格式：**
```javascript
// 成功响应
{
  "code": 0,
  "msg": "操作成功",
  "timestamp": 1754404981222,
  "data": { ... }
}

// 错误响应
{
  "error": "错误信息",
  "code": 400,
  "timestamp": "2025-08-05T14:40:04.193Z"
}
```

**错误处理：**
```javascript
import { handleApiError, ApiError } from '@/lib/error-handler';

export async function GET(request) {
  try {
    // 业务逻辑
    return NextResponse.json({ success: true });
  } catch (error) {
    return handleApiError(error);
  }
}
```

### 3. 组件开发规范

**组件文件结构：**
```javascript
// components/ui/Button.js
import React from 'react';

/**
 * 按钮组件
 * @param {Object} props - 组件属性
 * @param {string} props.variant - 按钮样式变体
 * @param {boolean} props.disabled - 是否禁用
 * @param {Function} props.onClick - 点击事件处理
 * @param {React.ReactNode} props.children - 子元素
 */
export default function Button({ 
  variant = 'primary', 
  disabled = false, 
  onClick, 
  children,
  ...props 
}) {
  return (
    <button
      className={`btn btn-${variant}`}
      disabled={disabled}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
}
```

### 4. 数据库开发规范

**模式定义：**
```prisma
// prisma/schema.prisma
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  username  String
  password  String
  role      String   @default("user")
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // 关联关系
  subscriptions Subscription[]
  
  @@map("users")
}
```

**数据库操作：**
```javascript
import { prisma } from '@/lib/prisma';

// 查询用户
const user = await prisma.user.findUnique({
  where: { email },
  include: { subscriptions: true }
});

// 创建用户
const newUser = await prisma.user.create({
  data: { email, username, password }
});
```

## 🧪 测试规范

### 单元测试
```javascript
// __tests__/api/auth.test.js
import { POST } from '@/app/api/auth/login/route';

describe('/api/auth/login', () => {
  it('should login successfully with valid credentials', async () => {
    const request = new Request('http://localhost:3000/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.token).toBeDefined();
  });
});
```

### 集成测试
```bash
# 运行所有测试
npm test

# 运行特定测试
npm test -- auth.test.js

# 生成覆盖率报告
npm run test:coverage
```

## 🔧 调试技巧

### 1. 服务器日志
```bash
# 查看实时日志
npm run pm2:logs

# 查看开发服务器日志
npm run dev
```

### 2. 数据库调试
```bash
# 打开数据库管理界面
npm run db:studio

# 查看数据库结构
npx prisma db pull
```

### 3. API 调试
```bash
# 健康检查
curl http://localhost:3000/api/health

# 测试登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123456"}'
```

### 4. 缓存调试
```bash
# 查看缓存状态
curl "http://localhost:3000/api/cache?action=status"

# 手动刷新缓存
npm run cache:refresh
```

## 📝 代码规范

### 1. 命名规范
- **变量**: camelCase (`userName`)
- **常量**: UPPER_SNAKE_CASE (`API_BASE_URL`)
- **函数**: camelCase (`getUserData`)
- **类**: PascalCase (`CacheService`)
- **文件**: kebab-case (`user-profile.js`)

### 2. 注释规范
```javascript
/**
 * 获取用户信息
 * @param {number} userId - 用户ID
 * @param {boolean} includeSubscription - 是否包含订阅信息
 * @returns {Promise<Object>} 用户信息对象
 */
async function getUserInfo(userId, includeSubscription = false) {
  // 实现逻辑
}
```

### 3. 错误处理规范
```javascript
import { ApiError, ValidationError } from '@/lib/error-handler';

// 业务错误
throw new ApiError('用户不存在', 404);

// 验证错误
throw new ValidationError('邮箱格式不正确', 'email');
```

## 🔄 Git 工作流

### 分支策略
- **main** - 主分支，生产环境代码
- **develop** - 开发分支，集成测试
- **feature/** - 功能分支
- **hotfix/** - 紧急修复分支

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加用户管理功能"

# 问题修复
git commit -m "fix: 修复登录验证问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 样式调整
git commit -m "style: 优化按钮样式"

# 重构代码
git commit -m "refactor: 重构缓存服务"

# 性能优化
git commit -m "perf: 优化数据库查询性能"

# 测试相关
git commit -m "test: 添加用户认证测试"
```

## 🚀 性能优化

### 1. 前端优化
- 使用 Next.js 图片优化
- 启用 Gzip 压缩
- 代码分割和懒加载
- 静态资源缓存

### 2. 后端优化
- 数据库查询优化
- 缓存机制
- API 响应压缩
- 连接池管理

### 3. 部署优化
- PM2 集群模式
- Nginx 反向代理
- SSL/TLS 优化
- CDN 加速

## 📞 开发支持

### 常见问题
1. **端口冲突** - 修改 `.env` 中的 `PORT` 配置
2. **数据库锁定** - 重启开发服务器
3. **缓存问题** - 运行 `npm run cache:refresh`
4. **权限问题** - 检查文件权限和用户角色

### 获取帮助
- 查看项目文档: `/docs/`
- 查看API文档: `/docs/API.md`
- 查看部署指南: `/docs/DEPLOYMENT.md`
