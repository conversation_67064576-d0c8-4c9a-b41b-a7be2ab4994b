#!/bin/bash

# 饰品监控助手 - 阿里云服务器快速启动脚本
# 用于快速启动后端服务
# 使用方法: ./start-server.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🚀 饰品监控助手 - 快速启动${NC}"
echo "=================================================="

# 检查环境
echo -e "${BLUE}🔍 检查运行环境...${NC}"

# 检查是否在项目目录
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ 请在项目根目录执行此脚本${NC}"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装${NC}"
    exit 1
fi

# 检查PM2
if ! command -v pm2 &> /dev/null; then
    echo -e "${YELLOW}📦 安装 PM2...${NC}"
    npm install -g pm2
fi

echo -e "${GREEN}✅ 环境检查完成${NC}"

# 检查是否已部署
if [ ! -f ".env.production" ] || [ ! -d "data" ] || [ ! -d ".next" ]; then
    echo -e "${YELLOW}⚠️ 检测到首次运行，执行完整部署...${NC}"
    chmod +x scripts/deploy-baota.sh
    ./scripts/deploy-baota.sh
else
    echo -e "${BLUE}🚀 启动生产服务...${NC}"
    
    # 停止现有进程
    pm2 stop youpin-sentinel 2>/dev/null || true
    
    # 启动应用
    pm2 start ecosystem.config.js --env production
    
    # 保存配置
    pm2 save
    
    echo -e "${GREEN}✅ 服务启动完成${NC}"
fi

# 验证启动
echo -e "${BLUE}🔍 验证服务状态...${NC}"
sleep 3

if pm2 list | grep -q "youpin-sentinel.*online"; then
    echo -e "${GREEN}✅ PM2 进程运行正常${NC}"
else
    echo -e "${RED}❌ 服务启动失败${NC}"
    pm2 logs youpin-sentinel --lines 5
    exit 1
fi

# 健康检查
if command -v curl &> /dev/null; then
    if curl -f -s http://localhost:3000/api/health > /dev/null; then
        echo -e "${GREEN}✅ 应用健康检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️ 应用可能还在启动中...${NC}"
    fi
fi

echo ""
echo -e "${GREEN}🎉 饰品监控助手启动成功！${NC}"
echo "=================================================="
echo -e "${BLUE}📋 服务信息:${NC}"
echo -e "  • 本地访问: ${YELLOW}http://localhost:3000${NC}"
echo -e "  • 管理员邮箱: ${YELLOW}<EMAIL>${NC}"
echo -e "  • 默认密码: ${YELLOW}Admin123456${NC}"
echo ""
echo -e "${BLUE}🔧 管理命令:${NC}"
echo -e "  • 查看状态: ${YELLOW}pm2 status${NC}"
echo -e "  • 查看日志: ${YELLOW}pm2 logs youpin-sentinel${NC}"
echo -e "  • 重启服务: ${YELLOW}pm2 restart youpin-sentinel${NC}"
echo -e "  • 健康检查: ${YELLOW}./scripts/health-check.sh${NC}"
echo ""
echo -e "${GREEN}✅ 现在请配置宝塔面板反向代理！${NC}"
echo -e "${BLUE}📖 配置指南: docs/ALIYUN_DEPLOYMENT.md${NC}"
