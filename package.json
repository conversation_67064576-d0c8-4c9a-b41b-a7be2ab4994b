{"name": "youpin-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:reset": "prisma db push --force-reset", "db:seed": "node scripts/create-super-admin.js", "deploy:local": "./scripts/deploy.sh local", "deploy:prod": "./scripts/deploy.sh production", "deploy:baota": "./scripts/deploy-baota.sh", "start:prod": "./scripts/start-production.sh", "start:server": "./start-server.sh", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop youpin-sentinel", "pm2:restart": "pm2 restart youpin-sentinel", "pm2:logs": "pm2 logs youpin-sentinel", "pm2:status": "pm2 status", "cache:refresh": "curl -X POST http://localhost:3000/api/cache -H \"Content-Type: application/json\" -d '{\"action\": \"refresh\"}'", "health": "curl http://localhost:3000/api/health", "health:check": "./scripts/health-check.sh", "backup": "./scripts/backup-sqlite.sh", "init:data": "node scripts/init-data.js"}, "dependencies": {"@prisma/client": "^6.12.0", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.4.1", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4"}}