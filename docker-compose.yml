version: '3.8'

services:
  # 主应用服务
  youpin-sentinel:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: youpin-sentinel
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:./data/production.db
      - JWT_SECRET=${JWT_SECRET:-change-this-jwt-secret-in-production}
      - NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL:-https://yourdomain.com}
      - PORT=3000
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env.production:/app/.env.production
    networks:
      - youpin-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: youpin-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      youpin-sentinel:
        condition: service_healthy
    networks:
      - youpin-network

  # 数据库备份服务（可选）
  backup:
    image: alpine:latest
    container_name: youpin-backup
    restart: "no"
    volumes:
      - ./data:/data:ro
      - ./backups:/backups
    command: >
      sh -c "
        apk add --no-cache sqlite &&
        mkdir -p /backups &&
        sqlite3 /data/production.db '.backup /backups/backup-$(date +%Y%m%d-%H%M%S).db' &&
        find /backups -name '*.db' -mtime +7 -delete
      "
    networks:
      - youpin-network

networks:
  youpin-network:
    driver: bridge

volumes:
  youpin-data:
    driver: local
  youpin-logs:
    driver: local
