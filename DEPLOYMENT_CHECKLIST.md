# ✅ 阿里云服务器部署检查清单

确保饰品监控助手在阿里云服务器上正确部署和运行的完整检查清单。

## 🔧 部署前检查

### 服务器环境
- [ ] 阿里云轻量应用服务器已购买（2核2G推荐）
- [ ] Ubuntu 20.04 LTS 系统已安装
- [ ] 服务器可以正常SSH连接
- [ ] 域名已解析到服务器IP地址

### 宝塔面板环境
- [ ] 宝塔面板 7.0+ 已安装
- [ ] 宝塔面板可以正常访问（端口8888）
- [ ] Node.js 版本管理器已安装
- [ ] PM2管理器已安装
- [ ] Nginx 1.18+ 已安装

### 项目准备
- [ ] 项目代码已上传到 `/www/wwwroot/youpin-sentinel/`
- [ ] 脚本文件有执行权限 (`chmod +x scripts/*.sh`)
- [ ] 项目所有者为 www 用户 (`chown -R www:www youpin-sentinel`)

## 🚀 部署过程检查

### 一键部署脚本
- [ ] `./scripts/deploy-baota.sh` 执行成功
- [ ] 没有报错信息
- [ ] 所有依赖安装完成
- [ ] 数据库初始化成功
- [ ] 应用构建完成
- [ ] PM2进程启动成功

### 环境配置
- [ ] `.env.production` 文件已生成
- [ ] JWT_SECRET 已设置为强随机密钥
- [ ] 域名配置正确（非 yourdomain.com）
- [ ] 数据库路径正确 (`./data/production.db`)

### 数据库初始化
- [ ] `data/` 目录已创建
- [ ] `production.db` 文件已生成
- [ ] 数据库表结构已创建
- [ ] 超级管理员账户已创建

### 应用构建
- [ ] `.next/` 目录已生成
- [ ] 构建过程无错误
- [ ] 静态资源已优化

## 🌐 宝塔面板配置检查

### 网站配置
- [ ] 网站已添加（域名: yourdomain.com）
- [ ] 网站根目录设置正确 (`/www/wwwroot/youpin-sentinel`)
- [ ] 网站状态为"正常运行"

### 反向代理配置
- [ ] 反向代理已添加
- [ ] 代理名称: `youpin-sentinel`
- [ ] 目标URL: `http://127.0.0.1:3000`
- [ ] 发送域名: `$host`
- [ ] 代理状态为"启用"

### SSL证书配置
- [ ] Let's Encrypt 证书已申请
- [ ] 证书状态为"有效"
- [ ] 强制HTTPS已开启
- [ ] 证书自动续期已启用

### 防火墙配置
- [ ] 端口80已开放（HTTP）
- [ ] 端口443已开放（HTTPS）
- [ ] 端口8888已开放（宝塔面板）
- [ ] 端口22已开放（SSH）

## 🔍 部署后验证

### PM2进程检查
```bash
# 检查PM2状态
pm2 status
# 应该显示 youpin-sentinel 进程为 online 状态

# 查看进程详情
pm2 show youpin-sentinel
# 检查内存使用、重启次数等信息
```

### 应用健康检查
```bash
# 本地健康检查
curl http://localhost:3000/api/health
# 应该返回 {"status": "ok", ...}

# 外网健康检查
curl https://yourdomain.com/api/health
# 应该返回相同的健康状态
```

### 功能测试
- [ ] 首页可以正常访问
- [ ] 登录页面可以正常访问
- [ ] 管理员账户可以正常登录
- [ ] 商品数据可以正常加载
- [ ] 缓存功能正常工作

### 性能检查
```bash
# 检查内存使用
free -h
# 应用内存使用应该 < 500MB

# 检查磁盘空间
df -h
# 磁盘使用应该 < 80%

# 检查响应时间
time curl -s https://yourdomain.com/api/health
# 响应时间应该 < 2秒
```

## 🔒 安全配置检查

### 密钥和密码
- [ ] JWT_SECRET 已更换为强随机密钥（非默认值）
- [ ] 管理员密码已修改（非 Admin123456）
- [ ] 宝塔面板密码已修改
- [ ] SSH密钥认证已配置（推荐）

### 文件权限
```bash
# 检查项目文件权限
ls -la /www/wwwroot/youpin-sentinel/
# 所有者应该是 www:www

# 检查数据库文件权限
ls -la /www/wwwroot/youpin-sentinel/data/
# 数据库文件应该是 644 权限
```

### 网络安全
- [ ] 防火墙已启用
- [ ] 不必要的端口已关闭
- [ ] SSH端口已修改（推荐）
- [ ] 宝塔面板端口已修改（推荐）

## 📊 监控配置检查

### 日志配置
- [ ] PM2日志正常记录
- [ ] Nginx访问日志正常记录
- [ ] 应用错误日志正常记录
- [ ] 日志轮转已配置

### 备份配置
```bash
# 检查备份脚本
ls -la scripts/backup-sqlite.sh
# 脚本应该有执行权限

# 测试备份功能
./scripts/backup-sqlite.sh
# 应该在 backups/ 目录生成备份文件
```

### 定时任务配置
- [ ] 数据缓存定时更新正常
- [ ] 数据库定时备份已设置（宝塔面板计划任务）
- [ ] 日志清理定时任务已设置

## 🎯 用户体验检查

### 前端功能
- [ ] 页面加载速度正常（< 3秒）
- [ ] 移动端适配正常
- [ ] 所有按钮和链接正常工作
- [ ] 表单提交正常

### 后端功能
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 数据查询功能正常
- [ ] 缓存刷新功能正常

### 管理功能
- [ ] 管理员登录正常
- [ ] 用户管理功能正常
- [ ] 支付确认功能正常
- [ ] 系统监控功能正常

## 🆘 故障排除检查

### 常见问题检查
```bash
# 1. 检查端口占用
netstat -tlnp | grep 3000

# 2. 检查进程状态
ps aux | grep node

# 3. 检查磁盘空间
df -h

# 4. 检查内存使用
free -h

# 5. 检查系统负载
uptime
```

### 日志检查
```bash
# PM2应用日志
pm2 logs youpin-sentinel --lines 50

# Nginx错误日志
tail -f /var/log/nginx/error.log

# 系统日志
tail -f /var/log/syslog
```

## 📋 最终验证清单

### 核心功能验证
- [ ] ✅ 网站可以通过域名正常访问
- [ ] ✅ HTTPS证书正常工作
- [ ] ✅ 管理员可以正常登录
- [ ] ✅ 商品数据正常显示
- [ ] ✅ 用户注册登录正常
- [ ] ✅ 支付功能正常（如果配置）

### 系统稳定性验证
- [ ] ✅ PM2进程稳定运行
- [ ] ✅ 内存使用正常（< 500MB）
- [ ] ✅ CPU使用正常（< 50%）
- [ ] ✅ 磁盘空间充足（< 80%）
- [ ] ✅ 网络连接稳定

### 安全性验证
- [ ] ✅ 强密码和密钥已设置
- [ ] ✅ 防火墙规则已配置
- [ ] ✅ SSL/TLS加密已启用
- [ ] ✅ 文件权限正确设置

### 运维准备验证
- [ ] ✅ 备份策略已实施
- [ ] ✅ 监控告警已配置
- [ ] ✅ 更新流程已测试
- [ ] ✅ 故障恢复方案已准备

## 🎉 部署完成

当所有检查项都通过时，你的饰品监控助手就已经成功部署到阿里云服务器了！

### 📞 后续支持
- **技术文档**: [docs/](docs/)
- **API接口**: [docs/API.md](docs/API.md)
- **运维指南**: [docs/ALIYUN_DEPLOYMENT.md](docs/ALIYUN_DEPLOYMENT.md)

### 🔧 常用命令
```bash
# 快速启动
./start-server.sh

# 健康检查
./scripts/health-check.sh

# 查看状态
pm2 status

# 查看日志
pm2 logs youpin-sentinel
```

---

**🎯 恭喜！你的饰品监控助手已在阿里云服务器上成功运行！**
