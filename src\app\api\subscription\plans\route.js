import { NextResponse } from 'next/server';

// 订阅计划配置
const SUBSCRIPTION_PLANS = [
  {
    id: 'monthly',
    name: '月度会员',
    description: '享受完整功能访问权限',
    price: 20,
    currency: 'CNY',
    duration: 30, // 天数
    features: [
      '实时价格监控',
      '高级筛选功能',
      '溢价商品筛选',
      '企业微信通知',
      '优先客服支持'
    ],
    popular: false
  },
  {
    id: 'yearly',
    name: '年度会员',
    description: '最优惠的选择，相当于10个月的价格',
    price: 120,
    currency: 'CNY',
    duration: 365, // 天数
    originalPrice: 240, // 原价（12个月 × 20元）
    discount: '50%',
    features: [
      '实时价格监控',
      '高级筛选功能',
      '溢价商品筛选',
      '企业微信通知',
      '优先客服支持',
      '数据导出功能',
      '专属客服群'
    ],
    popular: true
  }
];

export async function GET() {
  try {
    return NextResponse.json({
      plans: SUBSCRIPTION_PLANS,
      message: '获取订阅计划成功'
    });
  } catch (error) {
    console.error('获取订阅计划错误:', error);
    return NextResponse.json(
      { error: '获取订阅计划失败' },
      { status: 500 }
    );
  }
}
