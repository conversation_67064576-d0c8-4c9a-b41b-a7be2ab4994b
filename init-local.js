const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 开始本地环境初始化...');

try {
  // 1. 生成 Prisma 客户端
  console.log('📦 生成 Prisma 客户端...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  
  // 2. 推送数据库模式
  console.log('🗄️ 初始化数据库...');
  execSync('npx prisma db push', { stdio: 'inherit' });
  
  // 3. 创建超级管理员
  console.log('👤 创建超级管理员...');
  execSync('node scripts/create-super-admin.js', { stdio: 'inherit' });
  
  console.log('✅ 本地环境初始化完成！');
  console.log('');
  console.log('🔑 默认管理员账户:');
  console.log('  邮箱: <EMAIL>');
  console.log('  密码: Admin123456');
  console.log('');
  console.log('🚀 现在可以运行以下命令启动应用:');
  console.log('  npm run dev');
  
} catch (error) {
  console.error('❌ 初始化失败:', error.message);
  process.exit(1);
}
