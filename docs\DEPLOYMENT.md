# 🚀 部署指南

详细的部署指南，支持多种环境和平台。

## 📋 目录

- [环境要求](#环境要求)
- [本地开发部署](#本地开发部署)
- [生产环境部署](#生产环境部署)
- [宝塔面板部署](#宝塔面板部署)
- [Docker部署](#docker部署)
- [故障排除](#故障排除)

## 🔧 环境要求

### 基础要求
- **Node.js**: 18.x 或更高版本
- **npm**: 8.x 或更高版本
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 20.04+

### 生产环境推荐配置
- **CPU**: 2核心
- **内存**: 2GB RAM
- **存储**: 40GB SSD
- **网络**: 5Mbps 带宽

## 💻 本地开发部署

### 方式一：一键部署（推荐）

**Linux/Mac:**
```bash
git clone https://github.com/your-username/youpin-sentinel.git
cd youpin-sentinel
chmod +x scripts/deploy.sh
./scripts/deploy.sh local
npm run dev
```

**Windows:**
```powershell
git clone https://github.com/your-username/youpin-sentinel.git
cd youpin-sentinel
.\scripts\deploy.ps1 local
npm run dev
```

### 方式二：手动部署

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/youpin-sentinel.git
   cd youpin-sentinel
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，设置数据库路径和JWT密钥
   ```

4. **初始化数据库**
   ```bash
   npx prisma generate
   npx prisma db push
   node scripts/create-super-admin.js
   ```

5. **启动开发服务器**
   ```bash
   npm run dev
   ```

## 🌐 生产环境部署

### 方式一：一键部署

```bash
# 在服务器上执行
git clone https://github.com/your-username/youpin-sentinel.git
cd youpin-sentinel
chmod +x scripts/deploy.sh
./scripts/deploy.sh production
```

### 方式二：手动部署

1. **准备服务器环境**
   ```bash
   # 安装 Node.js 18+
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # 安装 PM2
   npm install -g pm2
   ```

2. **部署应用**
   ```bash
   # 克隆项目
   git clone https://github.com/your-username/youpin-sentinel.git
   cd youpin-sentinel
   
   # 安装生产依赖
   npm ci --only=production
   
   # 配置环境变量
   cp .env.production.example .env.production
   # 编辑 .env.production，设置正确的域名和密钥
   
   # 初始化数据库
   NODE_ENV=production npx prisma generate
   NODE_ENV=production npx prisma db push
   NODE_ENV=production node scripts/create-super-admin.js
   
   # 构建应用
   npm run build
   
   # 启动 PM2
   pm2 start ecosystem.config.js --env production
   pm2 save
   pm2 startup
   ```

3. **配置 Nginx 反向代理**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       
       location / {
           proxy_pass http://127.0.0.1:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## 🎛️ 宝塔面板部署

### 前置要求
- 已安装宝塔面板 7.0+
- 已安装 Node.js 版本管理器
- 已安装 PM2 管理器
- 已安装 Nginx

### 部署步骤

1. **上传项目**
   ```bash
   cd /www/wwwroot/
   git clone https://github.com/your-username/youpin-sentinel.git
   chown -R www:www youpin-sentinel
   ```

2. **一键部署**
   ```bash
   cd /www/wwwroot/youpin-sentinel
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh production
   ```

3. **宝塔面板配置**
   
   **添加网站：**
   - 进入宝塔面板 → 网站
   - 点击"添加站点"
   - 域名：`yourdomain.com`
   - 不创建数据库和FTP
   
   **配置反向代理：**
   - 进入网站设置 → 反向代理
   - 代理名称：`youpin-sentinel`
   - 目标URL：`http://127.0.0.1:3000`
   - 发送域名：`$host`
   
   **申请SSL证书：**
   - 进入网站设置 → SSL
   - 选择 Let's Encrypt
   - 申请免费证书
   - 开启强制 HTTPS

4. **验证部署**
   ```bash
   # 检查 PM2 状态
   pm2 status
   
   # 查看应用日志
   pm2 logs youpin-sentinel
   
   # 测试健康检查
   curl localhost:3000/api/health
   ```

## 🐳 Docker 部署

### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY prisma ./prisma/

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 生成 Prisma 客户端
RUN npx prisma generate

# 构建应用
RUN npm run build

# 创建数据目录
RUN mkdir -p data logs

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npm", "start"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  youpin-sentinel:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:./data/production.db
      - JWT_SECRET=your-jwt-secret-key
      - NEXT_PUBLIC_BASE_URL=https://yourdomain.com
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - youpin-sentinel
    restart: unless-stopped
```

### 部署命令
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f youpin-sentinel

# 停止服务
docker-compose down
```

## 🔧 故障排除

### 常见问题

**1. 端口被占用**
```bash
# 查看端口占用
netstat -tlnp | grep 3000
# 或
lsof -i :3000

# 停止占用进程
kill -9 <PID>
```

**2. 数据库连接失败**
```bash
# 检查数据库文件权限
ls -la data/
# 修复权限
chmod 755 data/
chmod 644 data/*.db
```

**3. PM2 进程异常**
```bash
# 查看 PM2 状态
pm2 status

# 重启应用
pm2 restart youpin-sentinel

# 查看详细日志
pm2 logs youpin-sentinel --lines 100
```

**4. 内存不足**
```bash
# 查看内存使用
free -h

# 设置 PM2 内存限制
pm2 restart youpin-sentinel --max-memory-restart 500M
```

### 日志位置
- **应用日志**: `pm2 logs youpin-sentinel`
- **Nginx日志**: `/var/log/nginx/`
- **系统日志**: `/var/log/syslog`

### 性能优化
1. **启用 Gzip 压缩**
2. **配置静态文件缓存**
3. **使用 CDN 加速**
4. **定期清理日志文件**

### 安全建议
1. **定期更新依赖包**
2. **使用强密码和密钥**
3. **配置防火墙规则**
4. **启用 HTTPS**
5. **定期备份数据库**

## 📞 技术支持

如果遇到问题，请提供：
1. **错误日志**: `pm2 logs youpin-sentinel`
2. **系统信息**: 服务器配置、操作系统版本
3. **操作步骤**: 详细的操作过程
4. **错误截图**: 浏览器控制台错误
