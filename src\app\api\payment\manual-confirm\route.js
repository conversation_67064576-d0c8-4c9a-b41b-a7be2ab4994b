import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserFromRequest } from '@/lib/auth';

// 手动确认支付
export async function POST(request) {
  try {
    const tokenPayload = getUserFromRequest(request);
    if (!tokenPayload) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { orderNo, confirmationCode, amount, planType } = await request.json();

    // 验证必要参数
    if (!orderNo || !confirmationCode || !amount || !planType) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 验证确认码（简单的验证逻辑）
    const expectedCode = generateConfirmationCode(orderNo, amount);
    if (confirmationCode.toUpperCase() !== expectedCode) {
      return NextResponse.json(
        { error: '确认码错误，请联系客服获取正确的确认码' },
        { status: 400 }
      );
    }

    // 检查是否已经有相同订单号的支付记录
    const existingPayment = await prisma.payment.findFirst({
      where: { transactionId: orderNo }
    });

    if (existingPayment && existingPayment.paymentStatus === 'completed') {
      return NextResponse.json(
        { error: '该订单已经完成支付' },
        { status: 400 }
      );
    }

    // 创建或更新支付记录
    let payment;
    if (existingPayment) {
      payment = await prisma.payment.update({
        where: { id: existingPayment.id },
        data: {
          paymentStatus: 'completed',
          completedAt: new Date()
        }
      });
    } else {
      payment = await prisma.payment.create({
        data: {
          userId: tokenPayload.userId,
          amount: parseFloat(amount),
          currency: 'CNY',
          paymentMethod: 'manual', // 手动确认
          paymentStatus: 'completed',
          transactionId: orderNo,
          completedAt: new Date()
        }
      });
    }

    // 创建订阅
    const subscription = await createSubscriptionFromPayment(payment, planType);

    return NextResponse.json({
      message: '支付确认成功，会员已激活',
      success: true,
      subscription: {
        planType: subscription.planType,
        endDate: subscription.endDate
      }
    });

  } catch (error) {
    console.error('手动确认支付错误:', error);
    return NextResponse.json(
      { error: '确认支付失败，请联系客服' },
      { status: 500 }
    );
  }
}

// 生成确认码（管理员用）
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const orderNo = searchParams.get('orderNo');
    const amount = searchParams.get('amount');

    if (!orderNo || !amount) {
      return NextResponse.json(
        { error: '缺少订单号或金额参数' },
        { status: 400 }
      );
    }

    const confirmationCode = generateConfirmationCode(orderNo, parseFloat(amount));

    return NextResponse.json({
      orderNo,
      amount,
      confirmationCode,
      message: '确认码生成成功'
    });

  } catch (error) {
    console.error('生成确认码错误:', error);
    return NextResponse.json(
      { error: '生成确认码失败' },
      { status: 500 }
    );
  }
}

// 生成确认码的函数
function generateConfirmationCode(orderNo, amount) {
  const crypto = require('crypto');
  const secret = 'youpin-payment-secret-2024'; // 在生产环境中应该使用环境变量
  const data = `${orderNo}-${amount}-${secret}`;
  const hash = crypto.createHash('md5').update(data).digest('hex');
  return hash.substring(0, 8).toUpperCase();
}

// 创建订阅
async function createSubscriptionFromPayment(payment, planType) {
  const { userId } = payment;
  
  let duration;
  if (planType === 'monthly') {
    duration = 30;
  } else if (planType === 'yearly') {
    duration = 365;
  } else {
    throw new Error('无效的订阅类型');
  }

  const now = new Date();
  const endDate = new Date(now.getTime() + duration * 24 * 60 * 60 * 1000);

  // 检查用户是否已有活跃订阅
  const existingSubscription = await prisma.subscription.findFirst({
    where: {
      userId,
      status: 'active',
      endDate: { gt: now }
    }
  });

  let subscription;
  if (existingSubscription) {
    // 如果有活跃订阅，延长时间
    const newEndDate = new Date(existingSubscription.endDate.getTime() + duration * 24 * 60 * 60 * 1000);
    subscription = await prisma.subscription.update({
      where: { id: existingSubscription.id },
      data: { endDate: newEndDate }
    });
  } else {
    // 创建新订阅
    subscription = await prisma.subscription.create({
      data: {
        userId,
        planType,
        status: 'active',
        startDate: now,
        endDate
      }
    });
  }

  // 关联支付记录和订阅
  await prisma.payment.update({
    where: { id: payment.id },
    data: { subscriptionId: subscription.id }
  });

  return subscription;
}
