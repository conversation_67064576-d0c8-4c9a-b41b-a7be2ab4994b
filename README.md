# 🎯 饰品监控助手价格监控系统

一个基于 Next.js 的 CS:GO 饰品价格监控和会员管理系统，专为阿里云轻量服务器 + 宝塔面板环境优化。

## ✨ 功能特性

- 🔍 **商品价格监控**: 实时监控 CS:GO 饰品价格变化
- 💰 **溢价分析**: 智能分析多普勒系列等热门饰品溢价情况
- 👥 **会员系统**: 完整的用户注册、登录和订阅管理
- 💳 **支付管理**: 支持手动确认支付和会员激活
- 📊 **数据展示**: 直观的价格趋势和统计信息
- ⏰ **定时缓存**: 自动定时更新数据，确保信息实时性
- 📱 **响应式设计**: 适配桌面端和移动端
- 🎨 **现代化界面**: 基于 Tailwind CSS 的美观设计

## 🛠️ 技术栈

- **前端**: Next.js 15, React 19, Tailwind CSS 4
- **后端**: Next.js API Routes
- **数据库**: SQLite + Prisma ORM
- **认证**: JWT Token
- **缓存**: 自定义缓存服务 + 定时任务调度器
- **外部API**: 悠悠有品API集成
- **部署**: 宝塔面板 + PM2 + Nginx

## 🚀 阿里云服务器部署

### 🚀 超快速启动（推荐）

```bash
# 1. 上传项目到服务器
cd /www/wwwroot/
git clone https://github.com/your-username/youpin-sentinel.git
chown -R www:www youpin-sentinel

# 2. 一键启动（自动检测是否需要部署）
cd youpin-sentinel
chmod +x start-server.sh
./start-server.sh

# 3. 配置宝塔面板反向代理（见文档）
```

### 方式二：完整部署流程

1. **上传项目到服务器**
   ```bash
   cd /www/wwwroot/
   git clone https://github.com/your-username/youpin-sentinel.git
   chown -R www:www youpin-sentinel
   ```

2. **一键部署**
   ```bash
   cd /www/wwwroot/youpin-sentinel
   chmod +x scripts/deploy-baota.sh
   ./scripts/deploy-baota.sh
   ```

3. **配置宝塔面板**
   - 添加网站（域名）
   - 配置反向代理到 `http://127.0.0.1:3000`
   - 申请 SSL 证书

### 方式二：通用Linux部署

```bash
# 本地开发环境
./scripts/deploy.sh local

# 生产环境
./scripts/deploy.sh production
```

## 📋 服务器要求

### 阿里云轻量应用服务器（推荐）
- **配置**: 2核2G内存，40GB SSD
- **系统**: Ubuntu 20.04 LTS
- **面板**: 宝塔面板 7.0+
- **软件**: Node.js 18+, PM2, Nginx

### 快速启动指南
🚀 **5分钟快速部署**: [ALIYUN_QUICKSTART.md](ALIYUN_QUICKSTART.md)
📖 **详细部署指南**: [docs/ALIYUN_DEPLOYMENT.md](docs/ALIYUN_DEPLOYMENT.md)

## 💻 本地开发

### 快速开始
```bash
# 1. 克隆项目
git clone https://github.com/your-username/youpin-sentinel.git
cd youpin-sentinel

# 2. 一键部署（推荐）
chmod +x scripts/deploy.sh
./scripts/deploy.sh local

# 3. 启动开发服务器
npm run dev
```

### 手动配置
如果需要手动配置，请按以下步骤：

1. **安装依赖**
   ```bash
   npm install
   ```

2. **环境配置**
   ```env
   DATABASE_URL="file:./data/dev.db"
   JWT_SECRET="your-jwt-secret-key"
   NEXT_PUBLIC_BASE_URL="http://localhost:3000"
   ```

3. **数据库初始化**
   ```bash
   npx prisma generate
   npx prisma db push
   node scripts/create-super-admin.js
   ```

4. **启动开发服务器**
   ```bash
   npm run dev
   ```

## 🔑 默认管理员账户

- **邮箱**: `<EMAIL>`
- **密码**: `Admin123456`
- ⚠️ **首次登录后必须修改密码！**

## 🎯 主要功能

### 👥 用户管理
- 用户注册和登录
- 个人资料管理
- 会员状态查看

### 📊 商品监控
- 实时价格数据获取
- 溢价商品筛选
- 价格趋势分析

### 💳 支付系统
- 订阅计划管理（月度 ¥20 / 年度 ¥120）
- 手动支付确认
- 会员权限控制

### 🔧 管理功能
- 超级管理员系统
- 用户管理（角色分配、状态管理）
- 支付确认码生成器
- 权限控制

## 📁 项目结构

```
youpin-sentinel/
├── src/                   # 源代码
│   ├── app/              # Next.js App Router
│   │   ├── admin/        # 管理员页面
│   │   ├── api/          # API 路由
│   │   ├── dashboard/    # 仪表板
│   │   └── ...           # 其他页面
│   ├── contexts/         # React Context
│   ├── data/             # 数据配置
│   └── lib/              # 工具库
├── scripts/              # 部署脚本
│   ├── baota-setup.sh    # 配置生成器
│   ├── deploy-baota.sh   # 一键部署
│   ├── backup-sqlite.sh  # 数据库备份
│   └── restore-sqlite.sh # 数据库恢复
├── prisma/               # 数据库
│   └── schema.prisma     # 数据库模式
└── public/               # 静态文件
```

## 🌐 宝塔面板详细部署指南

### 📋 服务器要求
- **配置**: 2核2G内存，40GB SSD（推荐）
- **系统**: Ubuntu 20.04+ / CentOS 7.6+
- **面板**: 宝塔面板 7.0+

### 🔧 宝塔面板软件安装
在宝塔面板 **软件商店** 安装：
- ✅ Nginx (1.20+)
- ✅ Node.js 版本管理器（安装 Node.js 18.x）
- ✅ PM2管理器

### 📝 详细部署步骤

#### 1. 生成配置文件
```bash
# 在本地运行，生成安全配置
./scripts/baota-setup.sh
```
这个脚本会：
- 自动生成强 JWT 密钥
- 创建生产环境配置文件
- 生成 PM2 配置
- 创建 Nginx 配置指南

#### 2. 上传项目
**方法一：宝塔文件管理器**
1. 将项目打包为 zip 文件
2. 在宝塔面板 → 文件 → 进入 `/www/wwwroot/`
3. 上传并解压

**方法二：Git 克隆（推荐）**
```bash
cd /www/wwwroot/
git clone https://github.com/your-username/youpin-sentinel.git
chown -R www:www youpin-sentinel
```

#### 3. 一键部署
```bash
cd /www/wwwroot/youpin-sentinel
./scripts/deploy-baota.sh
```
这个脚本会自动：
- ✅ 检查环境配置
- ✅ 安装项目依赖
- ✅ 初始化数据库
- ✅ 构建生产版本
- ✅ 创建超级管理员
- ✅ 启动 PM2 进程

#### 4. 宝塔面板网站配置
1. **添加网站**
   - 进入宝塔面板 → 网站
   - 点击 "添加站点"
   - 域名：`yourdomain.com`
   - 不创建数据库和FTP

2. **配置反向代理**
   - 进入网站设置 → 反向代理
   - 代理名称：`youpin-sentinel`
   - 目标URL：`http://127.0.0.1:3000`
   - 发送域名：`$host`

3. **申请 SSL 证书**
   - 进入网站设置 → SSL
   - 选择 Let's Encrypt
   - 申请免费证书
   - 开启强制 HTTPS

### 🔒 安全配置检查清单
- ✅ JWT 密钥已更换（非默认值）
- ✅ 管理员密码已修改
- ✅ 文件权限正确设置
- ✅ HTTPS 已启用
- ✅ 防火墙已配置

### 📊 监控和维护

#### 应用管理命令
```bash
# 查看应用状态
pm2 status

# 查看应用日志
pm2 logs youpin-sentinel

# 重启应用
pm2 restart youpin-sentinel

# 停止应用
pm2 stop youpin-sentinel

# 查看资源使用
pm2 monit
```

#### 数据库备份
```bash
# 手动备份
./scripts/backup-sqlite.sh

# 恢复数据库
./scripts/restore-sqlite.sh

# 设置定时备份（宝塔面板 → 计划任务）
# 任务类型：Shell脚本
# 执行周期：每天 02:00
# 脚本内容：
cd /www/wwwroot/youpin-sentinel && ./scripts/backup-sqlite.sh
```

#### 应用更新
```bash
# 更新代码
cd /www/wwwroot/youpin-sentinel
git pull

# 安装新依赖
npm install

# 重新构建
npm run build

# 重启应用
pm2 restart youpin-sentinel
```

## 🆘 故障排除

### 常见问题及解决方案

#### 1. 应用无法启动
**症状**: PM2 显示应用状态为 "errored" 或 "stopped"

**解决方案**:
```bash
# 查看详细错误日志
pm2 logs youpin-sentinel

# 检查环境变量配置
cat .env.production

# 检查 Node.js 版本
node -v

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

#### 2. 502 Bad Gateway 错误
**症状**: 访问网站显示 502 错误

**解决方案**:
```bash
# 检查应用是否运行
pm2 status

# 检查端口占用
netstat -tlnp | grep 3000

# 检查 Nginx 配置
nginx -t

# 重启 Nginx
systemctl restart nginx
```

#### 3. 数据库连接失败
**症状**: 应用日志显示数据库连接错误

**解决方案**:
```bash
# 检查数据库文件权限
ls -la /www/wwwroot/youpin-sentinel/data/

# 修复权限
chown -R www:www /www/wwwroot/youpin-sentinel/data/
chmod -R 755 /www/wwwroot/youpin-sentinel/data/

# 重新初始化数据库
cd /www/wwwroot/youpin-sentinel
npx prisma db push
```

#### 4. 内存不足
**症状**: 应用频繁重启，系统卡顿

**解决方案**:
```bash
# 查看内存使用
free -h

# 设置 PM2 内存限制
pm2 restart youpin-sentinel --max-memory-restart 500M

# 查看应用资源使用
pm2 monit
```

### 检查命令大全

```bash
# 系统状态检查
df -h                    # 磁盘空间
free -h                  # 内存使用
top                      # 进程状态
systemctl status nginx  # Nginx 状态

# 应用状态检查
pm2 status              # PM2 进程状态
pm2 logs youpin-sentinel # 应用日志
pm2 monit               # 资源监控

# 网络检查
netstat -tlnp | grep 3000  # 端口占用
curl localhost:3000/api/health  # 健康检查
curl https://yourdomain.com/api/health  # 外网访问

# 文件权限检查
ls -la /www/wwwroot/youpin-sentinel/
ls -la /www/wwwroot/youpin-sentinel/data/
```

## 📞 技术支持

### 日志位置
- **应用日志**: `pm2 logs youpin-sentinel`
- **Nginx访问日志**: `/www/wwwlogs/youpin-sentinel.log`
- **Nginx错误日志**: `/www/wwwlogs/youpin-sentinel.error.log`
- **系统日志**: `/var/log/syslog`

### 性能优化建议
1. **启用 Gzip 压缩**: 已在 Nginx 配置中启用
2. **静态文件缓存**: 已配置长期缓存
3. **数据库优化**: SQLite 已针对读取优化
4. **内存管理**: PM2 自动重启机制

### 安全建议
1. **定期更新**: 保持系统和依赖包更新
2. **备份策略**: 每日自动备份数据库
3. **监控告警**: 配置宝塔面板监控告警
4. **访问控制**: 限制管理员账户数量

### 联系支持
如果遇到无法解决的问题，请提供：
1. **错误日志**: `pm2 logs youpin-sentinel`
2. **系统信息**: 服务器配置、宝塔版本
3. **操作步骤**: 详细的操作过程
4. **错误截图**: 浏览器控制台错误

---

## 📄 许可证

MIT License

---

## 🎉 部署完成检查清单

部署完成后，请逐一检查以下项目：

### ✅ 基础功能
- [ ] 网站首页可正常访问
- [ ] 健康检查接口正常: `/api/health`
- [ ] 管理员可正常登录
- [ ] 用户注册功能正常

### ✅ 管理功能
- [ ] 用户管理页面正常
- [ ] 支付确认码生成正常
- [ ] 数据统计显示正确

### ✅ 安全配置
- [ ] HTTPS 证书正常
- [ ] JWT 密钥已更换
- [ ] 管理员密码已修改
- [ ] 文件权限正确

### ✅ 运维配置
- [ ] PM2 进程正常运行
- [ ] 数据库备份计划已设置
- [ ] 监控告警已配置
- [ ] 日志可正常查看

**🚀 恭喜！你的饰品监控助手价格监控系统已成功部署！**
