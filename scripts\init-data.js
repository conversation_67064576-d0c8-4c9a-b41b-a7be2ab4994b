#!/usr/bin/env node

/**
 * 数据初始化脚本
 * 在项目部署时执行一次，预加载缓存数据
 * 避免用户访问时触发初始化，提升用户体验
 */

const { PrismaClient } = require('@prisma/client');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'production';

async function initializeData() {
  console.log('🚀 开始初始化缓存数据...');
  console.log(`📋 运行环境: ${process.env.NODE_ENV}`);
  
  const prisma = new PrismaClient();
  
  try {
    // 检查数据库连接
    console.log('🔍 检查数据库连接...');
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ 数据库连接正常');

    // 动态导入调度器
    const { Scheduler } = await import('../src/lib/scheduler.js');
    
    // 初始化各等级数据
    const tiers = ['T1', 'T2', 'T3'];
    const results = {};
    
    for (const tier of tiers) {
      console.log(`\n📦 正在初始化 ${tier} 等级数据...`);
      
      try {
        // 执行数据获取
        await Scheduler.executeManualUpdate(tier);
        
        // 验证数据是否成功缓存
        const cacheEntry = await prisma.cacheData.findUnique({
          where: {
            tier_dataType: { tier, dataType: 'templates' }
          }
        });
        
        if (cacheEntry && cacheEntry.data) {
          const data = JSON.parse(cacheEntry.data);
          const count = data.results ? data.results.length : 0;
          console.log(`✅ ${tier} 等级数据初始化成功 (${count} 条记录)`);
          results[tier] = { success: true, count };
        } else {
          console.log(`⚠️ ${tier} 等级数据初始化后未找到缓存`);
          results[tier] = { success: false, count: 0 };
        }
        
      } catch (error) {
        console.error(`❌ ${tier} 等级数据初始化失败:`, error.message);
        results[tier] = { success: false, error: error.message };
        
        // 创建占位符缓存，避免用户看到完全空白
        try {
          const placeholderData = {
            tier: tier,
            totalTemplates: 0,
            results: [],
            message: '数据正在更新中，请稍后刷新页面',
            isPlaceholder: true,
            lastUpdate: new Date().toISOString()
          };
          
          await prisma.cacheData.upsert({
            where: {
              tier_dataType: { tier, dataType: 'templates' }
            },
            update: {
              data: JSON.stringify(placeholderData),
              updatedAt: new Date()
            },
            create: {
              tier,
              dataType: 'templates',
              data: JSON.stringify(placeholderData),
              createdAt: new Date(),
              updatedAt: new Date()
            }
          });
          
          console.log(`📝 ${tier} 等级占位符缓存已创建`);
        } catch (placeholderError) {
          console.error(`❌ 创建 ${tier} 等级占位符失败:`, placeholderError.message);
        }
      }
      
      // 等待一下，避免请求过于频繁
      if (tier !== tiers[tiers.length - 1]) {
        console.log('⏳ 等待 2 秒...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    // 输出总结
    console.log('\n📊 初始化结果总结:');
    console.log('================================');
    
    let successCount = 0;
    let totalItems = 0;
    
    for (const [tier, result] of Object.entries(results)) {
      if (result.success) {
        console.log(`✅ ${tier}: 成功 (${result.count} 条)`);
        successCount++;
        totalItems += result.count;
      } else {
        console.log(`❌ ${tier}: 失败 - ${result.error || '未知错误'}`);
      }
    }
    
    console.log('================================');
    console.log(`📈 成功率: ${successCount}/${tiers.length} (${Math.round(successCount/tiers.length*100)}%)`);
    console.log(`📦 总数据量: ${totalItems} 条记录`);
    
    if (successCount === tiers.length) {
      console.log('\n🎉 所有等级数据初始化完成！');
      console.log('💡 用户访问时将直接从缓存读取数据');
      process.exit(0);
    } else if (successCount > 0) {
      console.log('\n⚠️ 部分等级数据初始化成功，系统可以正常运行');
      console.log('💡 失败的等级将在定时任务中重试');
      process.exit(0);
    } else {
      console.log('\n❌ 所有等级数据初始化失败');
      console.log('💡 建议检查网络连接和外部API配置');
      console.log('💡 系统仍可启动，数据将在定时任务中重试获取');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 数据初始化过程中发生严重错误:', error);
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查数据库连接配置');
    console.log('2. 确保所有依赖已正确安装');
    console.log('3. 检查网络连接');
    console.log('4. 查看详细错误日志');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 执行初始化
if (require.main === module) {
  initializeData().catch(error => {
    console.error('❌ 初始化失败:', error);
    process.exit(1);
  });
}

module.exports = { initializeData };
