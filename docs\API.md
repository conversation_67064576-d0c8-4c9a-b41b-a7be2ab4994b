# 🔌 API 文档

饰品监控助手的 RESTful API 接口文档。

## 📋 目录

- [认证接口](#认证接口)
- [用户接口](#用户接口)
- [商品监控接口](#商品监控接口)
- [订阅管理接口](#订阅管理接口)
- [支付接口](#支付接口)
- [管理员接口](#管理员接口)
- [缓存管理接口](#缓存管理接口)
- [系统接口](#系统接口)

## 🔐 认证接口

### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "用户名"
}
```

**响应示例：**
```json
{
  "message": "注册成功",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "用户名",
    "role": "user"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 用户登出
```http
POST /api/auth/logout
Authorization: Bearer <token>
```

## 👤 用户接口

### 获取用户信息
```http
GET /api/user/profile
Authorization: Bearer <token>
```

### 更新用户信息
```http
PUT /api/user/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "新用户名",
  "email": "<EMAIL>"
}
```

### 修改密码
```http
PUT /api/user/password
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword"
}
```

## 📊 商品监控接口

### 获取模板数据
```http
GET /api/templates?tier=T1&refresh=false
```

**参数说明：**
- `tier`: 等级 (T1, T2, T3)
- `refresh`: 是否强制刷新缓存 (true/false)

**响应示例：**
```json
{
  "code": 0,
  "msg": "成功（来自缓存）",
  "timestamp": 1754404981222,
  "data": {
    "tier": "T1",
    "totalTemplates": 61,
    "totalItems": 1250,
    "results": [
      {
        "templateName": "爪子刀（★） | 多普勒",
        "dopplerName": "Phase 1",
        "items": [...]
      }
    ],
    "premiumItems": [...],
    "premiumItemsCount": 15,
    "fromCache": true,
    "cachedAt": "2025-08-05T14:40:04.193Z"
  }
}
```

### 获取溢价商品
```http
GET /api/premium-items?tier=T1
```

## 💳 订阅管理接口

### 获取订阅计划
```http
GET /api/subscription/plans
```

### 获取用户订阅状态
```http
GET /api/subscription/status
Authorization: Bearer <token>
```

### 创建订阅
```http
POST /api/subscription/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "planType": "monthly", // monthly 或 yearly
  "paymentMethod": "alipay" // alipay 或 wechat
}
```

## 💰 支付接口

### 创建支付订单
```http
POST /api/payment/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "planType": "monthly",
  "paymentMethod": "alipay"
}
```

### 手动确认支付
```http
POST /api/payment/manual-confirm
Content-Type: application/json

{
  "orderNo": "YP1754404981222",
  "amount": 20,
  "confirmCode": "CONFIRM123"
}
```

### 获取支付确认码
```http
GET /api/payment/manual-confirm?orderNo=YP1754404981222&amount=20
```

## 🔧 管理员接口

### 获取所有用户
```http
GET /api/admin/users
Authorization: Bearer <super_admin_token>
```

### 更新用户状态
```http
PUT /api/admin/users/:userId
Authorization: Bearer <super_admin_token>
Content-Type: application/json

{
  "isActive": true,
  "role": "user"
}
```

### 删除用户
```http
DELETE /api/admin/users/:userId
Authorization: Bearer <super_admin_token>
```

## 🗄️ 缓存管理接口

### 获取缓存状态
```http
GET /api/cache?action=status
```

### 手动刷新缓存
```http
POST /api/cache
Content-Type: application/json

{
  "action": "refresh",
  "tier": "T1" // 可选，不指定则刷新所有等级
}
```

### 启动/停止定时任务
```http
POST /api/cache
Content-Type: application/json

{
  "action": "start-scheduler" // 或 "stop-scheduler"
}
```

### 清理过期缓存
```http
DELETE /api/cache
```

## 🏥 系统接口

### 健康检查
```http
GET /api/health
```

**响应示例：**
```json
{
  "status": "ok",
  "timestamp": "2025-08-05T14:40:04.193Z",
  "database": "connected",
  "stats": {
    "totalUsers": 2,
    "activeSubscriptions": 1
  },
  "version": "0.1.0",
    "environment": "development"
}
```

## 📝 响应格式

### 成功响应
```json
{
  "code": 0,
  "msg": "操作成功",
  "timestamp": 1754404981222,
  "data": { ... }
}
```

### 错误响应
```json
{
  "error": "错误信息",
  "code": 400,
  "timestamp": "2025-08-05T14:40:04.193Z"
}
```

## 🔑 认证说明

大部分接口需要在请求头中包含 JWT Token：

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 📊 状态码说明

- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误
- `503` - 服务不可用

## 🚀 使用示例

### JavaScript/Node.js
```javascript
// 获取模板数据
const response = await fetch('/api/templates?tier=T1', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const data = await response.json();
```

### cURL
```bash
# 获取健康状态
curl -X GET http://localhost:3000/api/health

# 用户登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123456"}'
```
