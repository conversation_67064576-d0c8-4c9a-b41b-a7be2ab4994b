# 🚀 阿里云服务器快速启动指南

5分钟快速在阿里云服务器上部署饰品监控助手。

## 📋 前置要求

### 服务器配置
- **阿里云轻量应用服务器**: 2核2G，40GB SSD
- **操作系统**: Ubuntu 20.04 LTS
- **已安装**: 宝塔面板 7.0+

### 必装软件（通过宝塔面板安装）
- ✅ **Node.js 版本管理器**
- ✅ **PM2管理器** 
- ✅ **Nginx 1.18+**

## 🚀 5分钟快速部署

### 第1步：上传项目（2分钟）

**方式一：Git克隆（推荐）**
```bash
# SSH连接到服务器，执行以下命令
cd /www/wwwroot/
git clone https://github.com/your-username/youpin-sentinel.git
chown -R www:www youpin-sentinel
```

**方式二：文件上传**
1. 将项目打包为 `youpin-sentinel.zip`
2. 通过宝塔面板文件管理器上传到 `/www/wwwroot/`
3. 解压并重命名为 `youpin-sentinel`

### 第2步：一键部署（2分钟）
```bash
# SSH连接到服务器，执行以下命令
cd /www/wwwroot/youpin-sentinel

# 给脚本执行权限
chmod +x scripts/*.sh

# 一键部署
./scripts/deploy-baota.sh
```

### 第3步：配置宝塔面板（1分钟）

1. **添加网站**
   - 进入宝塔面板 → 网站 → 添加站点
   - 域名：`yourdomain.com`
   - 根目录：`/www/wwwroot/youpin-sentinel`
   - 不创建数据库和FTP

2. **配置反向代理**
   - 进入网站设置 → 反向代理 → 添加反向代理
   - 代理名称：`youpin-sentinel`
   - 目标URL：`http://127.0.0.1:3000`
   - 发送域名：`$host`

3. **申请SSL证书**
   - 进入网站设置 → SSL → Let's Encrypt
   - 申请免费证书
   - 开启强制HTTPS

## ✅ 验证部署

### 检查服务状态
```bash
# 检查PM2进程
pm2 status

# 查看应用日志
pm2 logs youpin-sentinel

# 健康检查
./scripts/health-check.sh

# 或直接访问
curl http://localhost:3000/api/health
```

### 访问应用
- **本地访问**: http://localhost:3000
- **域名访问**: https://yourdomain.com

### 默认管理员账户
- **邮箱**: `<EMAIL>`
- **密码**: `Admin123456`
- **⚠️ 首次登录后请立即修改密码！**

## 🔧 常用管理命令

### 应用管理
```bash
# 重启应用
pm2 restart youpin-sentinel

# 停止应用
pm2 stop youpin-sentinel

# 查看详细状态
pm2 show youpin-sentinel

# 查看资源监控
pm2 monit
```

### 数据管理
```bash
# 手动备份数据库
./scripts/backup-sqlite.sh

# 刷新缓存数据
curl -X POST http://localhost:3000/api/cache \
  -H "Content-Type: application/json" \
  -d '{"action": "refresh"}'
```

### 应用更新
```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm ci --only=production

# 重新构建
npm run build

# 重启应用
pm2 restart youpin-sentinel
```

## 🆘 常见问题

### 1. 应用无法启动
```bash
# 查看错误日志
pm2 logs youpin-sentinel --err

# 检查端口占用
netstat -tlnp | grep 3000

# 重新部署
./scripts/deploy-baota.sh
```

### 2. 网站无法访问
- 检查域名解析是否正确
- 检查宝塔面板反向代理配置
- 检查防火墙设置（开放80、443端口）
- 检查SSL证书状态

### 3. 数据加载失败
```bash
# 手动刷新缓存
curl -X POST http://localhost:3000/api/cache \
  -H "Content-Type: application/json" \
  -d '{"action": "refresh"}'

# 检查外部API连接
curl "http://localhost:3000/api/templates?tier=T1"
```

### 4. 内存不足
```bash
# 重启应用释放内存
pm2 restart youpin-sentinel

# 设置内存限制
pm2 restart youpin-sentinel --max-memory-restart 500M

# 查看内存使用
free -h
```

## 📞 技术支持

### 获取帮助
- **完整部署指南**: [docs/ALIYUN_DEPLOYMENT.md](docs/ALIYUN_DEPLOYMENT.md)
- **API文档**: [docs/API.md](docs/API.md)
- **开发指南**: [docs/DEVELOPMENT.md](docs/DEVELOPMENT.md)

### 问题反馈
如果遇到问题，请提供：
1. **服务器信息**: `uname -a`
2. **PM2状态**: `pm2 status`
3. **应用日志**: `pm2 logs youpin-sentinel --lines 50`
4. **错误截图**: 浏览器控制台错误

## 🎯 下一步

部署完成后，你可以：

1. **🔐 修改管理员密码** - 首次登录后立即修改
2. **⚙️ 配置外部API** - 设置悠悠有品API密钥（可选）
3. **💳 配置支付接口** - 设置支付宝/微信支付（可选）
4. **📊 查看数据监控** - 访问仪表板查看商品价格数据
5. **👥 管理用户系统** - 通过管理后台管理用户和订阅

## 🔒 安全提醒

- ✅ 修改默认管理员密码
- ✅ 设置强JWT密钥
- ✅ 配置防火墙规则
- ✅ 启用HTTPS
- ✅ 定期备份数据
- ✅ 监控系统资源

---

**🎉 恭喜！你的饰品监控助手已成功部署到阿里云服务器！**
