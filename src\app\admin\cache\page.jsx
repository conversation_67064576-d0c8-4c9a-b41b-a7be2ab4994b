'use client';

import { useState, useEffect } from 'react';

export default function CacheManagePage() {
  const [cacheStatus, setCacheStatus] = useState([]);
  const [schedulerStatus, setSchedulerStatus] = useState(null);
  const [recentTasks, setRecentTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // 获取缓存状态
  const fetchCacheStatus = async () => {
    try {
      const response = await fetch('/api/cache?action=status');
      const result = await response.json();
      
      if (result.code === 0) {
        setCacheStatus(result.data.cacheStatus);
        setSchedulerStatus(result.data.schedulerStatus);
        setRecentTasks(result.data.recentTasks);
      }
    } catch (error) {
      console.error('获取缓存状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 手动刷新缓存
  const handleRefreshCache = async (tier = null) => {
    setRefreshing(true);
    try {
      const response = await fetch('/api/cache', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'refresh',
          tier: tier
        })
      });
      
      const result = await response.json();
      if (result.code === 0) {
        alert(`缓存刷新成功: ${result.data.message}`);
        // 延迟刷新状态，等待缓存更新完成
        setTimeout(fetchCacheStatus, 2000);
      } else {
        alert(`缓存刷新失败: ${result.msg}`);
      }
    } catch (error) {
      console.error('刷新缓存失败:', error);
      alert('刷新缓存失败');
    } finally {
      setRefreshing(false);
    }
  };

  // 清理过期缓存
  const handleCleanExpiredCache = async () => {
    try {
      const response = await fetch('/api/cache?action=expired', {
        method: 'DELETE'
      });
      
      const result = await response.json();
      if (result.code === 0) {
        alert(`过期缓存清理完成: ${result.data.message}`);
        fetchCacheStatus();
      } else {
        alert(`清理失败: ${result.msg}`);
      }
    } catch (error) {
      console.error('清理缓存失败:', error);
      alert('清理缓存失败');
    }
  };

  // 格式化时间
  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai'
    });
  };

  // 格式化文件大小
  const formatSize = (bytes) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  useEffect(() => {
    fetchCacheStatus();
    // 每30秒自动刷新状态
    const interval = setInterval(fetchCacheStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">缓存管理</h1>
          <p className="mt-2 text-gray-600">管理系统缓存和定时任务</p>
        </div>

        {/* 操作按钮 */}
        <div className="mb-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">快速操作</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => handleRefreshCache()}
              disabled={refreshing}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {refreshing ? '刷新中...' : '刷新所有缓存'}
            </button>
            <button
              onClick={() => handleRefreshCache('T1')}
              disabled={refreshing}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              刷新T1缓存
            </button>
            <button
              onClick={() => handleRefreshCache('T2')}
              disabled={refreshing}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              刷新T2缓存
            </button>
            <button
              onClick={handleCleanExpiredCache}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700"
            >
              清理过期缓存
            </button>
            <button
              onClick={fetchCacheStatus}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              刷新状态
            </button>
          </div>
        </div>

        {/* 定时任务状态 */}
        {schedulerStatus && (
          <div className="mb-8 bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">定时任务状态</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">运行状态</p>
                <p className={`text-lg font-semibold ${schedulerStatus.isRunning ? 'text-green-600' : 'text-red-600'}`}>
                  {schedulerStatus.isRunning ? '运行中' : '已停止'}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">下次执行时间</p>
                <p className="text-lg font-semibold text-blue-600">
                  {schedulerStatus.nextCheck ? formatTime(schedulerStatus.nextCheck) : '未知'}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">最后执行</p>
                <p className="text-lg font-semibold text-gray-600">
                  {schedulerStatus.lastExecution ? formatTime(schedulerStatus.lastExecution) : '暂无记录'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 缓存状态 */}
        <div className="mb-8 bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold">缓存状态</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">等级</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">大小</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">过期时间</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {cacheStatus.map((cache, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{cache.tier}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{cache.dataType}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        cache.isExpired ? 'bg-red-100 text-red-800' :
                        !cache.isValid ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {cache.isExpired ? '已过期' : !cache.isValid ? '无效' : '有效'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatSize(cache.dataSize)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatTime(cache.createdAt)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatTime(cache.expiresAt)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* 最近任务 */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold">最近任务</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">等级</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentTasks.map((task) => (
                  <tr key={task.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{task.id}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {task.taskType === 'scheduled' ? '定时任务' : '手动任务'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{task.tier || '全部'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        task.status === 'completed' ? 'bg-green-100 text-green-800' :
                        task.status === 'failed' ? 'bg-red-100 text-red-800' :
                        task.status === 'running' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {task.status === 'completed' ? '已完成' :
                         task.status === 'failed' ? '失败' :
                         task.status === 'running' ? '运行中' : '等待中'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatTime(task.createdAt)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {task.completedAt ? formatTime(task.completedAt) : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
