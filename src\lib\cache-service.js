import { prisma } from './prisma.js';

/**
 * 缓存服务类
 * 负责管理悠悠接口数据的缓存
 */
export class CacheService {
  
  /**
   * 获取缓存数据
   * @param {string} tier - 等级 (T1, T2, T3)
   * @param {string} dataType - 数据类型 ('templates', 'premium-items')
   * @returns {Object|null} 缓存的数据或null
   */
  static async getCachedData(tier, dataType = 'templates') {
    try {
      const cacheEntry = await prisma.cacheData.findUnique({
        where: {
          tier_dataType: { tier, dataType }
        }
      });

      if (!cacheEntry || !this._isCacheValid(cacheEntry)) {
        return null;
      }

      // 解析JSON数据
      try {
        return JSON.parse(cacheEntry.data);
      } catch (parseError) {
        console.error(`缓存数据解析失败 [${tier}-${dataType}]:`, parseError);
        // 标记缓存为无效
        await this.invalidateCache(tier, dataType);
        return null;
      }
    } catch (error) {
      console.error(`获取缓存数据失败 [${tier}-${dataType}]:`, error);
      return null;
    }
  }

  /**
   * 检查缓存是否有效
   * @private
   */
  static _isCacheValid(cacheEntry) {
    const now = new Date();
    return cacheEntry.expiresAt > now && cacheEntry.isValid;
  }

  /**
   * 设置缓存数据
   * @param {string} tier - 等级
   * @param {string} dataType - 数据类型
   * @param {Object} data - 要缓存的数据
   * @param {number} ttlMinutes - 缓存时间（分钟），默认30分钟
   */
  static async setCachedData(tier, dataType, data, ttlMinutes = 30) {
    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + ttlMinutes * 60 * 1000);
      const jsonData = JSON.stringify(data);

      await prisma.cacheData.upsert({
        where: {
          tier_dataType: { tier, dataType }
        },
        update: {
          data: jsonData,
          updatedAt: now,
          expiresAt,
          isValid: true
        },
        create: {
          tier,
          dataType,
          data: jsonData,
          expiresAt,
          isValid: true
        }
      });

      console.log(`缓存已更新: ${tier}-${dataType}, 过期时间: ${expiresAt.toISOString()}`);
    } catch (error) {
      console.error(`设置缓存数据失败 [${tier}-${dataType}]:`, error);
      throw error;
    }
  }

  /**
   * 使缓存失效
   * @param {string} tier - 等级
   * @param {string} dataType - 数据类型
   */
  static async invalidateCache(tier, dataType) {
    try {
      await prisma.cacheData.updateMany({
        where: {
          tier: tier,
          dataType: dataType
        },
        data: {
          isValid: false
        }
      });
      console.log(`缓存已失效: ${tier}-${dataType}`);
    } catch (error) {
      console.error('使缓存失效失败:', error);
    }
  }

  /**
   * 清理过期缓存
   */
  static async cleanExpiredCache() {
    try {
      const now = new Date();
      const result = await prisma.cacheData.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: now } },
            { isValid: false }
          ]
        }
      });
      console.log(`清理了 ${result.count} 条过期缓存`);
      return result.count;
    } catch (error) {
      console.error('清理过期缓存失败:', error);
      return 0;
    }
  }

  /**
   * 获取缓存状态
   * @returns {Array} 所有缓存的状态信息
   */
  static async getCacheStatus() {
    try {
      const caches = await prisma.cacheData.findMany({
        orderBy: [
          { tier: 'asc' },
          { dataType: 'asc' }
        ]
      });

      const now = new Date();
      return caches.map(cache => ({
        tier: cache.tier,
        dataType: cache.dataType,
        isValid: cache.isValid,
        isExpired: cache.expiresAt < now,
        createdAt: cache.createdAt,
        updatedAt: cache.updatedAt,
        expiresAt: cache.expiresAt,
        dataSize: cache.data.length
      }));
    } catch (error) {
      console.error('获取缓存状态失败:', error);
      return [];
    }
  }

  /**
   * 创建缓存任务记录
   * @param {string} taskType - 任务类型 ('scheduled', 'manual')
   * @param {string} tier - 等级（可选）
   * @returns {Object} 创建的任务记录
   */
  static async createCacheTask(taskType, tier = null) {
    try {
      const task = await prisma.cacheTask.create({
        data: {
          taskType: taskType,
          tier: tier,
          status: 'pending'
        }
      });
      return task;
    } catch (error) {
      console.error('创建缓存任务失败:', error);
      throw error;
    }
  }

  /**
   * 更新缓存任务状态
   * @param {number} taskId - 任务ID
   * @param {string} status - 状态 ('running', 'completed', 'failed')
   * @param {string} errorMessage - 错误信息（可选）
   */
  static async updateCacheTask(taskId, status, errorMessage = null) {
    try {
      const updateData = {
        status: status
      };

      if (status === 'running') {
        updateData.startedAt = new Date();
      } else if (status === 'completed' || status === 'failed') {
        updateData.completedAt = new Date();
      }

      if (errorMessage) {
        updateData.errorMessage = errorMessage;
      }

      await prisma.cacheTask.update({
        where: { id: taskId },
        data: updateData
      });
    } catch (error) {
      console.error('更新缓存任务状态失败:', error);
    }
  }

  /**
   * 获取最近的缓存任务
   * @param {number} limit - 返回数量限制
   * @returns {Array} 任务列表
   */
  static async getRecentCacheTasks(limit = 10) {
    try {
      return await prisma.cacheTask.findMany({
        orderBy: { createdAt: 'desc' },
        take: limit
      });
    } catch (error) {
      console.error('获取缓存任务失败:', error);
      return [];
    }
  }
}
