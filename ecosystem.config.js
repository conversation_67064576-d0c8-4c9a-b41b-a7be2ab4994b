// PM2 生产环境配置文件

module.exports = {
  apps: [
    {
      name: 'youpin-sentinel',
      script: 'npm',
      args: 'start',
      cwd: './',
      instances: 1, // 单实例模式，避免SQLite并发问题
      exec_mode: 'fork', // fork模式
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 进程管理
      max_memory_restart: '500M', // 内存超过 500M 时重启
      min_uptime: '10s', // 最小运行时间
      max_restarts: 10, // 最大重启次数
      
      // 监控配置
      watch: false, // 生产环境不启用文件监控
      ignore_watch: [
        'node_modules',
        'logs',
        '.git',
        '.next'
      ],
      
      // 健康检查
      health_check_grace_period: 3000,
      
      // 自动重启配置
      autorestart: true,
      
      // 时间配置
      time: true,
      
      // 合并日志
      merge_logs: true,
      
      // 进程标题
      instance_var: 'INSTANCE_ID'
    }
  ],

  // 部署配置（可选）
  deploy: {
    production: {
      user: 'www', // 宝塔面板默认用户
      host: ['your-server-ip'], // 服务器 IP 地址
      ref: 'origin/main', // Git 分支
      repo: 'https://github.com/your-username/youpin-sentinel.git', // Git 仓库地址
      path: '/www/wwwroot/youpin-sentinel', // 宝塔面板默认路径
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --only=production && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
