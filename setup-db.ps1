Write-Host "🚀 开始数据库初始化..." -ForegroundColor Green

Write-Host "📦 生成 Prisma 客户端..." -ForegroundColor Blue
npx prisma generate

Write-Host "🗄️ 推送数据库模式..." -ForegroundColor Blue
npx prisma db push

Write-Host "👤 创建超级管理员..." -ForegroundColor Blue
node scripts/create-super-admin.js

Write-Host "✅ 数据库初始化完成！" -ForegroundColor Green
Write-Host ""
Write-Host "🔑 默认管理员账户:" -ForegroundColor Yellow
Write-Host "  邮箱: <EMAIL>"
Write-Host "  密码: Admin123456"
Write-Host ""
Write-Host "🚀 现在可以运行 npm run dev 启动应用" -ForegroundColor Green
