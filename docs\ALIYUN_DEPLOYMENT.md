# 🚀 阿里云服务器部署指南

专为阿里云轻量应用服务器 + 宝塔面板环境优化的详细部署指南。

## 📋 服务器要求

### 推荐配置
- **CPU**: 2核心
- **内存**: 2GB RAM  
- **存储**: 40GB SSD
- **带宽**: 5Mbps
- **系统**: Ubuntu 20.04 LTS / CentOS 7.6+

### 必装软件
- **宝塔面板**: 7.0+ 版本
- **Node.js**: 18.x 版本
- **PM2管理器**: 最新版本
- **Nginx**: 1.18+ 版本

## 🛠️ 服务器环境准备

### 1. 安装宝塔面板
```bash
# Ubuntu/Debian
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh

# CentOS
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
```

### 2. 宝塔面板基础配置
1. **登录宝塔面板**
   - 访问: `http://your-server-ip:8888`
   - 使用安装时显示的账号密码登录

2. **安装必要软件**
   - 进入 "软件商店"
   - 安装: Nginx 1.18+
   - 安装: Node.js 版本管理器
   - 安装: PM2管理器

3. **配置Node.js环境**
   ```bash
   # 通过宝塔面板或SSH安装Node.js 18
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # 验证安装
   node --version
   npm --version
   ```

## 🚀 项目部署

### 方式一：一键部署（推荐）

1. **上传项目到服务器**
   ```bash
   # 方法1: Git克隆（推荐）
   cd /www/wwwroot/
   git clone https://github.com/your-username/youpin-sentinel.git
   chown -R www:www youpin-sentinel
   
   # 方法2: 文件上传
   # 通过宝塔面板文件管理器上传项目压缩包并解压
   ```

2. **执行一键部署脚本**
   ```bash
   cd /www/wwwroot/youpin-sentinel
   chmod +x scripts/deploy-baota.sh
   ./scripts/deploy-baota.sh
   ```

### 方式二：手动部署

1. **安装项目依赖**
   ```bash
   cd /www/wwwroot/youpin-sentinel
   npm ci --only=production
   ```

2. **配置环境变量**
   ```bash
   # 复制并编辑生产环境配置
   cp .env.production .env.production.local
   nano .env.production.local
   
   # 生成强JWT密钥
   echo "JWT_SECRET=\"$(openssl rand -base64 32)\"" >> .env.production.local
   
   # 设置正确的域名
   sed -i 's/yourdomain.com/your-actual-domain.com/g' .env.production.local
   ```

3. **初始化数据库**
   ```bash
   # 创建数据目录
   mkdir -p data logs
   chown -R www:www data logs
   
   # 初始化数据库
   NODE_ENV=production npx prisma generate
   NODE_ENV=production npx prisma db push
   
   # 创建超级管理员
   NODE_ENV=production node scripts/create-super-admin.js
   ```

4. **构建应用**
   ```bash
   npm run build
   ```

5. **启动PM2进程**
   ```bash
   # 启动应用
   pm2 start ecosystem.config.js --env production
   
   # 保存PM2配置
   pm2 save
   pm2 startup
   ```

## 🌐 宝塔面板网站配置

### 1. 添加网站
1. 进入宝塔面板 → **网站**
2. 点击 **"添加站点"**
3. 填写配置：
   - **域名**: `yourdomain.com`
   - **根目录**: `/www/wwwroot/youpin-sentinel`
   - **PHP版本**: 纯静态
   - **数据库**: 不创建
   - **FTP**: 不创建

### 2. 配置反向代理
1. 进入网站设置 → **反向代理**
2. 点击 **"添加反向代理"**
3. 填写配置：
   - **代理名称**: `youpin-sentinel`
   - **目标URL**: `http://127.0.0.1:3000`
   - **发送域名**: `$host`
   - **内容替换**: 不启用

### 3. 申请SSL证书
1. 进入网站设置 → **SSL**
2. 选择 **Let's Encrypt**
3. 点击 **"申请"**
4. 开启 **"强制HTTPS"**

### 4. 配置伪静态（可选）
```nginx
# 在网站设置 → 伪静态 中添加
location /api/ {
    proxy_pass http://127.0.0.1:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 🔧 生产环境管理

### PM2 进程管理
```bash
# 查看进程状态
pm2 status

# 查看应用日志
pm2 logs youpin-sentinel

# 重启应用
pm2 restart youpin-sentinel

# 停止应用
pm2 stop youpin-sentinel

# 查看资源监控
pm2 monit

# 查看详细信息
pm2 show youpin-sentinel
```

### 应用更新流程
```bash
# 1. 备份数据库
./scripts/backup-sqlite.sh

# 2. 拉取最新代码
git pull origin main

# 3. 安装新依赖
npm ci --only=production

# 4. 重新构建
npm run build

# 5. 重启应用
pm2 restart youpin-sentinel

# 6. 验证部署
./scripts/health-check.sh
```

### 数据库备份
```bash
# 手动备份
./scripts/backup-sqlite.sh

# 设置定时备份（宝塔面板 → 计划任务）
# 任务类型：Shell脚本
# 执行周期：每天 02:00
# 脚本内容：
cd /www/wwwroot/youpin-sentinel && ./scripts/backup-sqlite.sh
```

### 日志管理
```bash
# 查看应用日志
pm2 logs youpin-sentinel --lines 100

# 查看Nginx日志
tail -f /www/server/nginx/logs/yourdomain.com.log

# 清理旧日志
pm2 flush youpin-sentinel
```

## 🔒 安全配置

### 1. 防火墙配置
```bash
# 开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw allow 8888  # 宝塔面板

# 启用防火墙
ufw enable
```

### 2. 宝塔面板安全设置
1. **修改面板端口**: 安全 → 面板设置 → 面板端口
2. **绑定域名**: 安全 → 面板设置 → 授权域名
3. **开启BasicAuth**: 安全 → 面板设置 → BasicAuth认证
4. **设置IP白名单**: 安全 → 面板设置 → 授权IP

### 3. 应用安全检查
```bash
# 检查文件权限
ls -la /www/wwwroot/youpin-sentinel/
ls -la /www/wwwroot/youpin-sentinel/data/

# 修复权限（如果需要）
chown -R www:www /www/wwwroot/youpin-sentinel/
chmod -R 755 /www/wwwroot/youpin-sentinel/
chmod 644 /www/wwwroot/youpin-sentinel/data/*.db
```

## 📊 监控和维护

### 1. 系统监控
```bash
# 查看系统资源
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
netstat -tlnp | grep 3000
```

### 2. 应用监控
```bash
# 健康检查
./scripts/health-check.sh

# 或者直接访问
curl https://yourdomain.com/api/health
```

### 3. 性能优化
1. **启用Nginx缓存**
2. **配置Gzip压缩**
3. **设置静态文件缓存**
4. **优化PM2配置**

## 🆘 故障排除

### 常见问题

**1. 应用无法启动**
```bash
# 检查PM2状态
pm2 status

# 查看错误日志
pm2 logs youpin-sentinel --err

# 检查端口占用
netstat -tlnp | grep 3000

# 重启应用
pm2 restart youpin-sentinel
```

**2. 数据库连接失败**
```bash
# 检查数据库文件
ls -la data/

# 检查权限
chmod 755 data/
chmod 644 data/*.db

# 重新初始化数据库
NODE_ENV=production npx prisma db push
```

**3. 反向代理不工作**
```bash
# 检查Nginx配置
nginx -t

# 重启Nginx
systemctl restart nginx

# 查看Nginx日志
tail -f /var/log/nginx/error.log
```

**4. SSL证书问题**
```bash
# 检查证书状态
openssl x509 -in /etc/letsencrypt/live/yourdomain.com/fullchain.pem -text -noout

# 手动续期证书
certbot renew --dry-run
```

### 性能问题

**1. 内存使用过高**
```bash
# 重启应用释放内存
pm2 restart youpin-sentinel

# 设置内存限制
pm2 restart youpin-sentinel --max-memory-restart 500M
```

**2. 响应速度慢**
```bash
# 检查缓存状态
curl "https://yourdomain.com/api/cache?action=status"

# 手动刷新缓存
curl -X POST "https://yourdomain.com/api/cache" -H "Content-Type: application/json" -d '{"action": "refresh"}'
```

## 📞 技术支持

### 联系方式
- **项目文档**: `/docs/`
- **API文档**: `/docs/API.md`
- **开发指南**: `/docs/DEVELOPMENT.md`

### 日志收集
如果遇到问题，请提供：
1. **PM2日志**: `pm2 logs youpin-sentinel`
2. **Nginx日志**: `/var/log/nginx/error.log`
3. **系统信息**: `uname -a`, `free -h`, `df -h`
4. **应用配置**: `.env.production`（隐藏敏感信息）

### 紧急恢复
```bash
# 快速恢复服务
cd /www/wwwroot/youpin-sentinel
pm2 stop youpin-sentinel
pm2 start ecosystem.config.js --env production

# 恢复数据库备份
./scripts/restore-sqlite.sh backup-20250805-020000.db
```

## ✅ 部署检查清单

### 部署前检查
- [ ] 服务器配置满足要求
- [ ] 宝塔面板已安装并配置
- [ ] Node.js 18+ 已安装
- [ ] PM2管理器已安装
- [ ] 域名已解析到服务器IP

### 部署后检查
- [ ] 应用正常启动 (`pm2 status`)
- [ ] 健康检查通过 (`./scripts/health-check.sh`)
- [ ] 网站可正常访问
- [ ] SSL证书已配置
- [ ] 管理员账户可正常登录
- [ ] 缓存数据正常加载
- [ ] 定时任务正常运行

### 安全检查
- [ ] JWT密钥已更换为强随机密钥
- [ ] 管理员密码已修改
- [ ] 文件权限正确设置
- [ ] 防火墙已配置
- [ ] 宝塔面板安全设置已启用

### 性能检查
- [ ] 内存使用正常 (<80%)
- [ ] 磁盘空间充足 (<80%)
- [ ] 响应时间正常 (<2秒)
- [ ] 缓存命中率正常
- [ ] 数据库查询性能正常

## 🎯 生产环境最佳实践

### 1. 安全最佳实践
- 定期更新系统和软件包
- 使用强密码和密钥
- 启用防火墙和入侵检测
- 定期备份数据
- 监控异常访问

### 2. 性能最佳实践
- 启用Nginx缓存和压缩
- 配置CDN加速
- 优化数据库查询
- 监控资源使用
- 定期清理日志

### 3. 运维最佳实践
- 设置监控告警
- 建立备份策略
- 制定应急预案
- 记录操作日志
- 定期健康检查

## 📈 扩展和优化

### 负载均衡（高流量场景）
```bash
# 启用PM2集群模式
pm2 start ecosystem.config.js --env production -i max

# 配置Nginx负载均衡
upstream youpin_backend {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
    server 127.0.0.1:3003;
}
```

### 数据库优化（大数据量场景）
```bash
# 考虑迁移到MySQL/PostgreSQL
# 配置数据库连接池
# 添加数据库索引
# 实施数据分片策略
```

### CDN配置（全球访问优化）
- 配置阿里云CDN
- 设置静态资源缓存
- 启用GZIP压缩
- 配置缓存策略

这个部署指南专门针对阿里云服务器环境，确保你能够顺利部署和运行饰品监控助手系统。
