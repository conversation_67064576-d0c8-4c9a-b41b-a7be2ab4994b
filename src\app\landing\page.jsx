'use client';

import Link from 'next/link';
import { useState } from 'react';

export default function LandingPage() {
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      icon: '💎',
      title: '实时价格监控',
      description: '24/7监控蝴蝶刀、爪子刀、M9刺刀等热门饰品价格变化',
      benefit: '第一时间发现价格波动，抓住投资机会'
    },
    {
      icon: '📊',
      title: '智能溢价分析',
      description: '自动计算饰品溢价率，筛选出低于10%溢价的优质商品',
      benefit: '避免高价购买，确保每次交易都有利润空间'
    },
    {
      icon: '🔔',
      title: '微信推送提醒',
      description: '发现低溢价商品时立即通过企业微信推送通知',
      benefit: '不错过任何赚钱机会，随时随地接收重要信息'
    },
    {
      icon: '⚡',
      title: '半点整点更新',
      description: '每30分钟自动更新数据，确保信息的时效性',
      benefit: '数据永远保持最新，决策基于最准确的市场信息'
    }
  ];

  const testimonials = [
    {
      name: '张先生',
      role: 'CS:GO投资者',
      content: '使用饰品监控助手3个月，成功抓住了多次蝴蝶刀价格波动，收益率提升了40%！',
      avatar: '👨‍💼'
    },
    {
      name: '李女士', 
      role: '饰品收藏家',
      content: '溢价分析功能太棒了，帮我避免了很多高价购买，现在每次交易都很有信心。',
      avatar: '👩‍💻'
    },
    {
      name: '王同学',
      role: '游戏玩家',
      content: '微信推送很及时，上课时也能收到通知，已经通过低价收购赚了好几千！',
      avatar: '🎮'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      {/* 导航栏 */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                🎯 饰品监控助手
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  预览功能
                </button>
              </Link>
              <Link href="/login">
                <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  登录
                </button>
              </Link>
              <Link href="/register">
                <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all">
                  免费注册
                </button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 英雄区域 */}
      <section className="relative overflow-hidden py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6">
              CS:GO饰品投资神器
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
              专业监控蝴蝶刀、爪子刀、M9刺刀价格波动<br/>
              <span className="text-2xl font-bold text-green-600">智能发现低溢价机会，月收益率可达20%+</span>
            </p>
            
            {/* 价格对比展示 */}
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-12 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">💰 真实收益案例</h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl mb-2">🦋</div>
                  <h4 className="font-bold text-gray-800">蝴蝶刀伽马多普勒p1</h4>
                  <div className="text-sm text-gray-600 mt-2">
                    <span className="line-through text-red-500">市场价: ¥33,000</span><br/>
                    <span className="text-green-600 font-bold">发现价: ¥31,650</span><br/>
                    <span className="text-blue-600 font-bold">节省: ¥1,350</span>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl mb-2">🗡️</div>
                  <h4 className="font-bold text-gray-800">爪子刀多普勒p4</h4>
                  <div className="text-sm text-gray-600 mt-2">
                    <span className="line-through text-red-500">市场价: ¥28,000</span><br/>
                    <span className="text-green-600 font-bold">发现价: ¥25,000</span><br/>
                    <span className="text-blue-600 font-bold">节省: ¥3,000</span>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl mb-2">⚔️</div>
                  <h4 className="font-bold text-gray-800">M9刺刀多普勒p2</h4>
                  <div className="text-sm text-gray-600 mt-2">
                    <span className="line-through text-red-500">市场价: ¥23,800</span><br/>
                    <span className="text-green-600 font-bold">发现价: ¥22,500</span><br/>
                    <span className="text-blue-600 font-bold">节省: ¥1,300 </span>
                  </div>
                </div>
              </div>
              <div className="mt-6 p-4 bg-green-50 rounded-lg">
                <p className="text-green-800 font-bold">
                  💡 仅上述3件饰品就能节省 <span className="text-2xl">¥5,650</span>，而会员费仅需 ¥20/月！
                </p>
              </div>
            </div>

            {/* CTA按钮 */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/register">
                <button className="bg-gradient-to-r from-green-500 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-green-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-xl">
                  🚀 立即免费注册 (0元体验)
                </button>
              </Link>
              <Link href="/dashboard">
                <button className="bg-white text-gray-800 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-gray-50 border-2 border-gray-200 transition-all duration-200">
                  👀 免费预览功能
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 功能特性 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">为什么选择饰品监控助手？</h2>
            <p className="text-xl text-gray-600">专业的饰品投资工具，让你的每一次交易都更有把握</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index}
                className="text-center p-6 rounded-2xl hover:shadow-lg transition-all duration-200 cursor-pointer"
                onMouseEnter={() => setActiveFeature(index)}
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">{feature.title}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <div className="text-sm text-blue-600 font-medium">{feature.benefit}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 用户评价 */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">用户真实反馈</h2>
            <p className="text-xl text-gray-600">看看其他用户是如何通过饰品监控助手获得收益的</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <div className="text-3xl mr-3">{testimonial.avatar}</div>
                  <div>
                    <h4 className="font-bold text-gray-800">{testimonial.name}</h4>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-gray-700 italic">"{testimonial.content}"</p>
                <div className="mt-4 flex text-yellow-400">
                  {'⭐'.repeat(5)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 定价区域 */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">超值定价，物超所值</h2>
          <p className="text-xl text-blue-100 mb-12">一次饰品交易的节省就能覆盖全年会员费用</p>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-gray-800 mb-4">月度会员</h3>
              <div className="text-4xl font-bold text-blue-600 mb-4">¥20<span className="text-lg text-gray-600">/月</span></div>
              <ul className="text-left space-y-2 mb-6">
                <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>实时价格监控</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>溢价智能分析</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>微信推送通知</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>专业数据支持</li>
              </ul>
              <Link href="/register">
                <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                  开始使用
                </button>
              </Link>
            </div>
            
            <div className="bg-white rounded-2xl p-8 shadow-xl border-4 border-yellow-400 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-yellow-900 px-4 py-1 rounded-full text-sm font-bold">
                🔥 推荐
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">年度会员</h3>
              <div className="text-4xl font-bold text-purple-600 mb-2">¥120<span className="text-lg text-gray-600">/年</span></div>
              <div className="text-sm text-green-600 font-bold mb-4">相当于¥10/月，节省50%！</div>
              <ul className="text-left space-y-2 mb-6">
                <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>月度会员所有功能</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>优先客服支持</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>新功能抢先体验</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>专属投资建议</li>
              </ul>
              <Link href="/register">
                <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all">
                  立即订阅
                </button>
              </Link>
            </div>
          </div>
          
          <div className="mt-12 text-center">
            <p className="text-blue-100 mb-4">💡 算一笔账：一次成功的低溢价交易就能赚回全年会员费</p>
            <p className="text-white font-bold text-lg">风险提示：投资有风险，决策需谨慎</p>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">常见问题</h2>
          </div>
          
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-bold text-gray-800 mb-2">❓ 数据准确性如何保证？</h3>
              <p className="text-gray-600">我们直接对接Steam市场API，每30分钟自动更新，确保数据的实时性和准确性。</p>
            </div>
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-bold text-gray-800 mb-2">❓ 溢价分析是如何计算的？</h3>
              <p className="text-gray-600">系统会自动计算当前售价与历史底价的差值，筛选出溢价10%以内的优质商品。</p>
            </div>
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-bold text-gray-800 mb-2">❓ 微信通知如何设置？</h3>
              <p className="text-gray-600">订阅后系统会自动发送企业微信通知，无需额外配置，确保你不错过任何机会。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 最终CTA */}
      <section className="py-20 bg-gradient-to-r from-green-500 to-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">准备开始你的饰品投资之旅？</h2>
          <p className="text-xl text-green-100 mb-8">加入数千名成功投资者的行列，让数据为你的决策保驾护航</p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <button className="bg-white text-green-600 px-8 py-4 rounded-xl text-lg font-bold hover:bg-gray-100 transform hover:scale-105 transition-all duration-200 shadow-xl">
                🎯 立即免费注册
              </button>
            </Link>
            <Link href="/dashboard">
              <button className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-white hover:text-green-600 transition-all duration-200">
                👀 先看看功能
              </button>
            </Link>
          </div>
          
          <p className="text-green-100 mt-6 text-sm">
            ⚡ 注册即可免费体验所有功能 | 💳 支持支付宝、微信支付 | 🔒 7天无理由退款
          </p>
        </div>
      </section>

      {/* 页脚 */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4">
            🎯 饰品监控助手
          </h3>
          <p className="text-gray-400 mb-4">专业的CS:GO饰品价格监控平台</p>
          <p className="text-gray-500 text-sm">© 2024 饰品监控助手. 保留所有权利.</p>
        </div>
      </footer>
    </div>
  );
}
