/**
 * 统一错误处理工具
 */

import { NextResponse } from 'next/server';

/**
 * API错误类
 */
export class ApiError extends Error {
  constructor(message, statusCode = 500, code = null) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
  }
}

/**
 * 业务错误类
 */
export class BusinessError extends Error {
  constructor(message, code = null) {
    super(message);
    this.name = 'BusinessError';
    this.code = code;
  }
}

/**
 * 验证错误类
 */
export class ValidationError extends Error {
  constructor(message, field = null) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

/**
 * 统一错误响应处理
 */
export function handleApiError(error) {
  console.error('API Error:', error);

  // API错误
  if (error instanceof ApiError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        timestamp: new Date().toISOString()
      },
      { status: error.statusCode }
    );
  }

  // 业务错误
  if (error instanceof BusinessError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        timestamp: new Date().toISOString()
      },
      { status: 400 }
    );
  }

  // 验证错误
  if (error instanceof ValidationError) {
    return NextResponse.json(
      {
        error: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      },
      { status: 400 }
    );
  }

  // Prisma错误
  if (error.code && error.code.startsWith('P')) {
    return NextResponse.json(
      {
        error: '数据库操作失败',
        code: error.code,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }

  // 默认错误
  return NextResponse.json(
    {
      error: '服务器内部错误',
      timestamp: new Date().toISOString()
    },
    { status: 500 }
  );
}

/**
 * 异步错误包装器
 */
export function asyncHandler(fn) {
  return async (request, context) => {
    try {
      return await fn(request, context);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

/**
 * 参数验证工具
 */
export class Validator {
  static required(value, fieldName) {
    if (value === undefined || value === null || value === '') {
      throw new ValidationError(`${fieldName} 是必填项`, fieldName);
    }
    return value;
  }

  static email(value, fieldName = 'email') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      throw new ValidationError(`${fieldName} 格式不正确`, fieldName);
    }
    return value;
  }

  static minLength(value, minLength, fieldName) {
    if (value.length < minLength) {
      throw new ValidationError(`${fieldName} 长度不能少于 ${minLength} 个字符`, fieldName);
    }
    return value;
  }

  static maxLength(value, maxLength, fieldName) {
    if (value.length > maxLength) {
      throw new ValidationError(`${fieldName} 长度不能超过 ${maxLength} 个字符`, fieldName);
    }
    return value;
  }

  static oneOf(value, allowedValues, fieldName) {
    if (!allowedValues.includes(value)) {
      throw new ValidationError(`${fieldName} 必须是以下值之一: ${allowedValues.join(', ')}`, fieldName);
    }
    return value;
  }

  static number(value, fieldName) {
    const num = Number(value);
    if (isNaN(num)) {
      throw new ValidationError(`${fieldName} 必须是数字`, fieldName);
    }
    return num;
  }

  static positiveNumber(value, fieldName) {
    const num = this.number(value, fieldName);
    if (num <= 0) {
      throw new ValidationError(`${fieldName} 必须是正数`, fieldName);
    }
    return num;
  }
}

/**
 * 日志工具
 */
export class Logger {
  static info(message, data = null) {
    console.log(`[INFO] ${new Date().toISOString()} - ${message}`, data || '');
  }

  static warn(message, data = null) {
    console.warn(`[WARN] ${new Date().toISOString()} - ${message}`, data || '');
  }

  static error(message, error = null) {
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || '');
  }

  static debug(message, data = null) {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${new Date().toISOString()} - ${message}`, data || '');
    }
  }
}

/**
 * 响应工具
 */
export class ResponseHelper {
  static success(data = null, message = '操作成功') {
    return NextResponse.json({
      code: 0,
      msg: message,
      timestamp: Date.now(),
      data
    });
  }

  static error(message, code = -1, statusCode = 400) {
    return NextResponse.json(
      {
        code,
        msg: message,
        timestamp: Date.now(),
        data: null
      },
      { status: statusCode }
    );
  }

  static paginated(items, total, page = 1, pageSize = 10) {
    return NextResponse.json({
      code: 0,
      msg: '获取成功',
      timestamp: Date.now(),
      data: {
        items,
        pagination: {
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    });
  }
}
