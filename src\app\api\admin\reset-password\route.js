import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireSuperAdmin, hashPassword, isValidPassword } from '@/lib/auth';

export async function PUT(request) {
  try {
    // 验证超级管理员权限
    const authResult = requireSuperAdmin(request);
    if (authResult.error) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { userId, newPassword } = await request.json();

    // 输入验证
    if (!userId || !newPassword) {
      return NextResponse.json(
        { error: '用户ID和新密码不能为空' },
        { status: 400 }
      );
    }

    if (!isValidPassword(newPassword)) {
      return NextResponse.json(
        { error: '新密码至少8位，需包含字母和数字' },
        { status: 400 }
      );
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: parseInt(userId) }
    });

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      );
    }

    // 加密新密码
    const newPasswordHash = await hashPassword(newPassword);

    // 更新密码
    const updatedUser = await prisma.user.update({
      where: { id: parseInt(userId) },
      data: {
        passwordHash: newPasswordHash,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      message: '用户密码重置成功',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        username: updatedUser.username
      }
    });

  } catch (error) {
    console.error('重置密码错误:', error);
    return NextResponse.json(
      { error: '重置密码失败，请稍后重试' },
      { status: 500 }
    );
  }
}
