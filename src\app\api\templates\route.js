import { NextResponse } from 'next/server';
import { CacheService } from '@/lib/cache-service';
import { Scheduler } from '@/lib/scheduler';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const tier = searchParams.get('tier') || 'T1';
    const forceRefresh = searchParams.get('refresh') === 'true';

    if (forceRefresh) {
      try {
        console.log(`收到强制刷新请求，正在更新 ${tier} 等级缓存...`);
        await Scheduler.executeManualUpdate(tier);
        console.log(`${tier} 等级缓存强制刷新完成`);
      } catch (refreshError) {
        console.error('强制刷新缓存失败:', refreshError);
      }
    }

    const cachedData = await CacheService.getCachedData(tier, 'templates');

    if (cachedData) {
      return NextResponse.json({
        code: 0,
        msg: forceRefresh ? '成功（已刷新缓存）' : '成功（来自缓存）',
        timestamp: Date.now(),
        data: {
          ...cachedData,
          fromCache: true,
          cachedAt: cachedData.cachedAt,
          refreshed: forceRefresh
        }
      });
    }

    return NextResponse.json({
      code: 1,
      msg: '暂无缓存数据，请稍后再试或联系管理员',
      timestamp: Date.now(),
      data: {
        tier: tier,
        totalTemplates: 0,
        results: [],
        fromCache: false,
        message: '系统正在初始化缓存数据，请稍后再试。数据将在每个整点和半点自动更新。'
      }
    });

  } catch (error) {
    console.error('API调用失败:', error);
    return NextResponse.json({
      code: -1,
      msg: '查询失败: ' + error.message,
      timestamp: Date.now(),
      data: null
    }, { status: 500 });
  }
}