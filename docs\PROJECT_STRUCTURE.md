# 📁 项目结构说明

饰品监控助手的详细项目结构和文件说明。

## 🏗️ 总体架构

```
youpin-sentinel/
├── 📁 src/                    # 源代码目录
│   ├── 📁 app/               # Next.js App Router
│   ├── 📁 components/        # React 组件
│   ├── 📁 lib/              # 工具库和服务
│   └── 📁 utils/            # 通用工具函数
├── 📁 prisma/               # 数据库配置
├── 📁 scripts/              # 部署和管理脚本
├── 📁 docs/                 # 项目文档
├── 📁 public/               # 静态资源
├── 📁 data/                 # 数据库文件（运行时生成）
├── 📁 logs/                 # 日志文件（运行时生成）
└── 📁 nginx/                # Nginx 配置
```

## 📂 详细目录结构

### `/src/app/` - Next.js 应用路由
```
src/app/
├── 📄 layout.js             # 根布局组件
├── 📄 page.js               # 首页
├── 📄 globals.css           # 全局样式
├── 📁 auth/                 # 认证相关页面
│   ├── 📄 login/page.js     # 登录页面
│   └── 📄 register/page.js  # 注册页面
├── 📁 dashboard/            # 用户仪表板
│   └── 📄 page.js           # 仪表板主页
├── 📁 admin/                # 管理员页面
│   ├── 📄 page.js           # 管理员主页
│   └── 📄 users/page.js     # 用户管理页面
├── 📁 subscription/         # 订阅管理页面
│   └── 📄 page.js           # 订阅页面
└── 📁 api/                  # API 路由
    ├── 📁 auth/             # 认证 API
    ├── 📁 user/             # 用户 API
    ├── 📁 admin/            # 管理员 API
    ├── 📁 subscription/     # 订阅 API
    ├── 📁 payment/          # 支付 API
    ├── 📁 cache/            # 缓存管理 API
    ├── 📁 templates/        # 模板数据 API
    ├── 📁 premium-items/    # 溢价商品 API
    └── 📄 health/route.js   # 健康检查 API
```

### `/src/components/` - React 组件
```
src/components/
├── 📁 ui/                   # 基础 UI 组件
│   ├── 📄 Button.js         # 按钮组件
│   ├── 📄 Input.js          # 输入框组件
│   ├── 📄 Modal.js          # 模态框组件
│   └── 📄 Loading.js        # 加载组件
├── 📁 layout/               # 布局组件
│   ├── 📄 Header.js         # 页头组件
│   ├── 📄 Footer.js         # 页脚组件
│   └── 📄 Sidebar.js        # 侧边栏组件
├── 📁 auth/                 # 认证组件
│   ├── 📄 LoginForm.js      # 登录表单
│   └── 📄 RegisterForm.js   # 注册表单
├── 📁 dashboard/            # 仪表板组件
│   ├── 📄 StatsCard.js      # 统计卡片
│   └── 📄 PriceChart.js     # 价格图表
└── 📁 admin/                # 管理员组件
    ├── 📄 UserTable.js      # 用户表格
    └── 📄 PaymentManager.js # 支付管理
```

### `/src/lib/` - 核心服务和工具
```
src/lib/
├── 📄 prisma.js             # Prisma 客户端配置
├── 📄 auth.js               # 认证工具函数
├── 📄 cache-service.js      # 缓存服务
├── 📄 scheduler.js          # 定时任务调度器
├── 📄 youpin-api.js         # 悠悠有品 API 服务
├── 📄 error-handler.js      # 错误处理工具
├── 📄 payment.js            # 支付处理
└── 📄 config.js             # 应用配置
```

### `/src/utils/` - 通用工具函数
```
src/utils/
├── 📄 format.js             # 格式化工具
├── 📄 validation.js         # 验证工具
├── 📄 constants.js          # 常量定义
└── 📄 helpers.js            # 辅助函数
```

### `/prisma/` - 数据库配置
```
prisma/
├── 📄 schema.prisma         # 数据库模式定义
└── 📁 migrations/           # 数据库迁移文件（如果使用）
```

### `/scripts/` - 部署和管理脚本
```
scripts/
├── 📄 deploy.sh             # Linux/Mac 部署脚本
├── 📄 deploy.ps1            # Windows 部署脚本
├── 📄 baota-setup.sh        # 宝塔面板配置脚本
├── 📄 deploy-baota.sh       # 宝塔面板部署脚本
├── 📄 create-super-admin.js # 创建超级管理员脚本
├── 📄 backup-sqlite.sh      # 数据库备份脚本
└── 📄 health-check.sh       # 健康检查脚本
```

### `/docs/` - 项目文档
```
docs/
├── 📄 API.md                # API 接口文档
├── 📄 DEPLOYMENT.md         # 部署指南
├── 📄 PROJECT_STRUCTURE.md  # 项目结构说明（本文件）
└── 📄 DEVELOPMENT.md        # 开发指南
```

## 🔧 核心文件说明

### 配置文件
- **`package.json`** - 项目依赖和脚本配置
- **`next.config.js`** - Next.js 框架配置
- **`tailwind.config.js`** - Tailwind CSS 配置
- **`ecosystem.config.js`** - PM2 进程管理配置
- **`.env.example`** - 环境变量模板
- **`.env.production`** - 生产环境配置模板

### 数据库相关
- **`prisma/schema.prisma`** - 数据库模式定义
- **`src/lib/prisma.js`** - Prisma 客户端配置

### 核心服务
- **`src/lib/cache-service.js`** - 缓存管理服务
- **`src/lib/scheduler.js`** - 定时任务调度器
- **`src/lib/youpin-api.js`** - 外部API集成服务
- **`src/lib/auth.js`** - 用户认证和授权

### 业务逻辑
- **`src/app/api/`** - 所有后端API接口
- **`src/components/`** - 前端React组件
- **`src/utils/`** - 通用工具函数

## 📊 数据流架构

```mermaid
graph TB
    A[用户请求] --> B[Next.js Router]
    B --> C[API Routes]
    C --> D[业务逻辑层]
    D --> E[缓存服务]
    D --> F[数据库 Prisma]
    D --> G[外部API]
    
    H[定时调度器] --> E
    H --> G
    
    E --> I[SQLite 缓存]
    F --> J[SQLite 数据库]
    G --> K[悠悠有品API]
```

## 🔄 缓存机制

1. **定时缓存更新** - 每30分钟自动更新商品数据
2. **手动缓存刷新** - 管理员可手动触发缓存更新
3. **分层缓存** - T1、T2等级分别缓存
4. **缓存失效** - 自动检测和清理过期缓存

## 🔐 安全机制

1. **JWT认证** - 基于Token的用户认证
2. **角色权限** - 用户、管理员、超级管理员三级权限
3. **输入验证** - 严格的参数验证和过滤
4. **错误处理** - 统一的错误处理和日志记录

## 🚀 部署架构

### 开发环境
```
开发者 → Next.js Dev Server (3000) → SQLite
```

### 生产环境
```
用户 → Nginx (80/443) → Next.js App (3000) → SQLite
                     ↓
                   PM2 进程管理
```

### Docker环境
```
用户 → Nginx Container → Next.js Container → Volume (SQLite)
```

## 📝 开发规范

### 文件命名
- **组件文件**: PascalCase (如 `UserTable.js`)
- **页面文件**: kebab-case (如 `user-profile.js`)
- **工具文件**: kebab-case (如 `error-handler.js`)
- **API路由**: kebab-case (如 `premium-items/route.js`)

### 代码组织
- **一个文件一个主要功能**
- **相关功能放在同一目录**
- **公共组件放在 `/components/ui/`**
- **业务组件放在对应的功能目录**

### 环境管理
- **开发环境**: `.env`
- **生产环境**: `.env.production`
- **敏感配置**: 使用环境变量，不提交到版本控制
