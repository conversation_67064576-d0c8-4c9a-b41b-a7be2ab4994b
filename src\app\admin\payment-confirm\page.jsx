'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function PaymentCodesPage() {
  const { user, token, loading: authLoading } = useAuth();
  const router = useRouter();
  const [orderNo, setOrderNo] = useState('');
  const [amount, setAmount] = useState('');
  const [confirmationCode, setConfirmationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [history, setHistory] = useState([]);

  // 检查超级管理员权限
  useEffect(() => {
    if (!authLoading) {
      if (!user || user.role !== 'super_admin') {
        router.push('/dashboard');
        return;
      }
    }
  }, [authLoading, user, router]);

  // 生成确认码
  const generateCode = async () => {
    if (!orderNo.trim() || !amount.trim()) {
      setError('请填写订单号和金额');
      return;
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      setError('请输入有效的金额');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      const response = await fetch(`/api/payment/manual-confirm?orderNo=${encodeURIComponent(orderNo)}&amount=${amountNum}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '生成确认码失败');
      }

      setConfirmationCode(data.confirmationCode);
      setSuccess('确认码生成成功！');

      // 添加到历史记录
      const newRecord = {
        id: Date.now(),
        orderNo: data.orderNo,
        amount: data.amount,
        confirmationCode: data.confirmationCode,
        timestamp: new Date().toLocaleString()
      };
      setHistory(prev => [newRecord, ...prev.slice(0, 9)]); // 保留最近10条记录

    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      setSuccess('已复制到剪贴板');
      setTimeout(() => setSuccess(''), 2000);
    });
  };

  // 清空表单
  const clearForm = () => {
    setOrderNo('');
    setAmount('');
    setConfirmationCode('');
    setError('');
    setSuccess('');
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            支付确认码生成器
          </h1>
          <p className="text-gray-600">管理员工具：为用户生成支付确认码</p>
          <div className="flex justify-center space-x-4 mt-4">
            <Link href="/dashboard">
              <button className="text-blue-600 hover:text-blue-500 transition-colors">
                ← 返回仪表板
              </button>
            </Link>
            <Link href="/admin">
              <button className="text-purple-600 hover:text-purple-500 transition-colors">
                系统管理 →
              </button>
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* 使用说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-800 mb-2">使用说明：</h3>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. 用户完成支付后，会提供订单号和支付金额</li>
              <li>2. 在下方输入订单号和金额，点击生成确认码</li>
              <li>3. 将生成的确认码提供给用户</li>
              <li>4. 用户输入确认码后，系统会自动激活会员</li>
            </ol>
          </div>

          {/* 输入表单 */}
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                订单号
              </label>
              <input
                type="text"
                value={orderNo}
                onChange={(e) => setOrderNo(e.target.value)}
                placeholder="例如：YP1705312200001"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                支付金额
              </label>
              <select
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择金额</option>
                <option value="20">¥20 (月度会员)</option>
                <option value="120">¥120 (年度会员)</option>
              </select>
            </div>

            {error && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
                {error}
              </div>
            )}

            {success && (
              <div className="p-3 bg-green-100 border border-green-400 text-green-700 rounded-md">
                {success}
              </div>
            )}

            <button
              onClick={generateCode}
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50"
            >
              {loading ? '生成中...' : '🔑 生成确认码'}
            </button>
          </div>

          {/* 确认码显示 */}
          {confirmationCode && (
            <div className="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-semibold text-green-800 mb-4">确认码生成成功！</h3>

              <div className="space-y-4">
                <div className="bg-white p-4 rounded-lg border">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-600">订单号：{orderNo}</p>
                      <p className="text-sm text-gray-600">金额：¥{amount}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">确认码：</p>
                      <p className="text-2xl font-bold text-green-600 font-mono">{confirmationCode}</p>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => copyToClipboard(confirmationCode)}
                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    📋 复制确认码
                  </button>

                  <button
                    onClick={() => copyToClipboard(`订单号：${orderNo}\n金额：¥${amount}\n确认码：${confirmationCode}`)}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    📋 复制全部信息
                  </button>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm text-yellow-800">
                    <strong>提醒：</strong>请将确认码 <code className="bg-yellow-200 px-1 rounded">{confirmationCode}</code> 提供给用户，用户在支付页面输入此确认码即可激活会员。
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 快速操作 */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="font-semibold text-gray-800 mb-4">快速操作</h3>
            <div className="grid md:grid-cols-2 gap-4">
              <button
                onClick={() => {
                  setOrderNo(`YP${Date.now()}${Math.random().toString(36).substr(2, 5)}`);
                  setAmount('20');
                }}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-left"
              >
                <div className="font-medium text-gray-800">生成月度会员订单</div>
                <div className="text-sm text-gray-600">自动填入订单号和¥20金额</div>
              </button>

              <button
                onClick={() => {
                  setOrderNo(`YP${Date.now()}${Math.random().toString(36).substr(2, 5)}`);
                  setAmount('120');
                }}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-left"
              >
                <div className="font-medium text-gray-800">生成年度会员订单</div>
                <div className="text-sm text-gray-600">自动填入订单号和¥120金额</div>
              </button>
            </div>
          </div>

          {/* 重置按钮 */}
          <div className="mt-6 text-center">
            <button
              onClick={clearForm}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              🔄 重置表单
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
