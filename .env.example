# ===========================================
# 饰品监控助手 - 环境配置模板
# ===========================================

# 环境类型 (development, production)
NODE_ENV="development"

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL="file:./data/dev.db"

# ===========================================
# 安全配置
# ===========================================
# JWT密钥（生产环境必须使用强随机密钥）
JWT_SECRET="your-super-secret-jwt-key-change-in-production"

# ===========================================
# 应用配置
# ===========================================
# 应用基础URL
NEXT_PUBLIC_BASE_URL="http://localhost:3000"

# 应用端口
PORT=3000

# ===========================================
# 外部API配置（可选）
# ===========================================
# 悠悠有品API配置
NEXT_PUBLIC_YOUPIN_APP_KEY="your-youpin-app-key"
NEXT_PUBLIC_YOUPIN_PRIVATE_KEY="your-youpin-private-key"

# ===========================================
# 支付配置（可选）
# ===========================================
# 支付宝支付配置
ALIPAY_APP_ID="your-alipay-app-id"
ALIPAY_PRIVATE_KEY="your-alipay-private-key"
ALIPAY_PUBLIC_KEY="your-alipay-public-key"

# 微信支付配置
WECHAT_APP_ID="your-wechat-app-id"
WECHAT_MCH_ID="your-wechat-mch-id"
WECHAT_API_KEY="your-wechat-api-key"

# ===========================================
# 缓存配置
# ===========================================
# 缓存过期时间（分钟）
CACHE_TTL_MINUTES=30

# 定时任务间隔（分钟）
SCHEDULER_INTERVAL_MINUTES=30

# ===========================================
# 日志配置
# ===========================================
# 日志级别 (debug, info, warn, error)
LOG_LEVEL="info"

# 日志文件路径
LOG_FILE_PATH="./logs"
