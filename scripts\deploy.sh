#!/bin/bash

# 饰品监控助手 - 通用部署脚本
# 适用于本地开发和生产环境
# 使用方法: ./scripts/deploy.sh [local|production]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取部署环境
DEPLOY_ENV=${1:-local}

echo -e "${GREEN}🚀 饰品监控助手部署脚本${NC}"
echo -e "${BLUE}部署环境: ${DEPLOY_ENV}${NC}"
echo "=================================================="

# 检查 Node.js 环境
check_nodejs() {
    echo -e "${BLUE}🔍 检查 Node.js 环境...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        echo -e "${YELLOW}请安装 Node.js 18+ 版本${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js 版本: $NODE_VERSION${NC}"
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm -v)
    echo -e "${GREEN}✅ npm 版本: $NPM_VERSION${NC}"
}

# 生成随机密钥
generate_secret() {
    if command -v openssl >/dev/null 2>&1; then
        openssl rand -base64 32
    elif command -v node >/dev/null 2>&1; then
        node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
    else
        echo "$(date +%s)-$(whoami)-$(hostname)" | base64 | head -c 32
    fi
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 安装项目依赖...${NC}"
    
    if [ "$DEPLOY_ENV" = "production" ]; then
        npm ci --only=production
    else
        npm install
    fi
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 配置环境变量
setup_environment() {
    echo -e "${BLUE}⚙️ 配置环境变量...${NC}"
    
    # 创建数据目录
    mkdir -p data logs
    
    # 生成环境配置
    if [ "$DEPLOY_ENV" = "production" ]; then
        ENV_FILE=".env.production"
        BASE_URL="https://yourdomain.com"
        DB_PATH="./data/production.db"
    else
        ENV_FILE=".env"
        BASE_URL="http://localhost:3000"
        DB_PATH="./data/dev.db"
    fi
    
    # 生成强密钥
    JWT_SECRET=$(generate_secret)
    
    # 创建环境配置文件
    cat > "$ENV_FILE" << EOF
# 数据库配置
DATABASE_URL="file:${DB_PATH}"

# JWT密钥
JWT_SECRET="${JWT_SECRET}"

# 应用基础URL
NEXT_PUBLIC_BASE_URL="${BASE_URL}"

# 环境配置
NODE_ENV="${DEPLOY_ENV}"
EOF
    
    echo -e "${GREEN}✅ 环境配置完成${NC}"
    echo -e "${YELLOW}⚠️ 请根据需要修改 ${ENV_FILE} 中的域名配置${NC}"
}

# 初始化数据库
init_database() {
    echo -e "${BLUE}🗄️ 初始化数据库...${NC}"
    
    # 设置环境变量
    if [ "$DEPLOY_ENV" = "production" ]; then
        export NODE_ENV=production
    fi
    
    # 生成 Prisma 客户端
    npx prisma generate
    
    # 推送数据库模式
    npx prisma db push
    
    echo -e "${GREEN}✅ 数据库初始化完成${NC}"
}

# 构建应用
build_application() {
    if [ "$DEPLOY_ENV" = "production" ]; then
        echo -e "${BLUE}🔨 构建生产版本...${NC}"
        npm run build
        echo -e "${GREEN}✅ 应用构建完成${NC}"
    else
        echo -e "${YELLOW}⏭️ 开发环境跳过构建步骤${NC}"
    fi
}

# 创建超级管理员
create_admin() {
    echo -e "${BLUE}👤 创建超级管理员...${NC}"
    
    if [ "$DEPLOY_ENV" = "production" ]; then
        export NODE_ENV=production
    fi
    
    if node scripts/create-super-admin.js; then
        echo -e "${GREEN}✅ 超级管理员创建完成${NC}"
    else
        echo -e "${YELLOW}⚠️ 超级管理员可能已存在${NC}"
    fi
}

# 启动应用
start_application() {
    if [ "$DEPLOY_ENV" = "production" ]; then
        echo -e "${BLUE}🚀 配置 PM2 进程管理...${NC}"
        
        # 检查 PM2 是否安装
        if ! command -v pm2 &> /dev/null; then
            echo -e "${YELLOW}📦 安装 PM2...${NC}"
            npm install -g pm2
        fi
        
        # 停止现有进程
        pm2 stop youpin-sentinel 2>/dev/null || true
        pm2 delete youpin-sentinel 2>/dev/null || true
        
        # 启动应用
        pm2 start ecosystem.config.js --env production
        pm2 save
        
        echo -e "${GREEN}✅ 生产环境启动完成${NC}"
        echo -e "${BLUE}🌐 访问地址: https://yourdomain.com${NC}"
        
    else
        echo -e "${GREEN}✅ 本地环境准备就绪${NC}"
        echo -e "${BLUE}🌐 访问地址: http://localhost:3000${NC}"
        echo -e "${YELLOW}现在可以运行以下命令启动应用:${NC}"
        echo -e "  ${GREEN}npm run dev${NC}   # 开发模式"
        echo -e "  ${GREEN}npm start${NC}     # 生产模式"
    fi
}

# 显示结果
show_result() {
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo "=================================================="
    echo -e "${BLUE}📋 部署信息:${NC}"
    echo -e "  • 环境: ${DEPLOY_ENV}"
    echo -e "  • 项目路径: $(pwd)"
    echo -e "  • 数据库: SQLite"
    echo ""
    echo -e "${BLUE}🔑 默认管理员账户:${NC}"
    echo -e "  • 邮箱: ${YELLOW}<EMAIL>${NC}"
    echo -e "  • 密码: ${YELLOW}Admin123456${NC}"
    echo -e "  • ${RED}⚠️ 请首次登录后立即修改密码！${NC}"
    echo ""
    
    if [ "$DEPLOY_ENV" = "production" ]; then
        echo -e "${BLUE}🔧 生产环境管理命令:${NC}"
        echo -e "  • 查看状态: ${YELLOW}pm2 status${NC}"
        echo -e "  • 查看日志: ${YELLOW}pm2 logs youpin-sentinel${NC}"
        echo -e "  • 重启应用: ${YELLOW}pm2 restart youpin-sentinel${NC}"
        echo -e "  • 停止应用: ${YELLOW}pm2 stop youpin-sentinel${NC}"
    else
        echo -e "${BLUE}🔧 开发环境管理命令:${NC}"
        echo -e "  • 查看数据库: ${YELLOW}npx prisma studio${NC}"
        echo -e "  • 重置数据库: ${YELLOW}npx prisma db push --force-reset${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}✅ 准备就绪！${NC}"
}

# 主函数
main() {
    check_nodejs
    install_dependencies
    setup_environment
    init_database
    build_application
    create_admin
    start_application
    show_result
}

# 执行主函数
main "$@"
