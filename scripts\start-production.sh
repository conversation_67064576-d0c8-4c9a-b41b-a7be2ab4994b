#!/bin/bash

# 饰品监控助手 - 生产环境启动脚本
# 专为阿里云服务器 + 宝塔面板环境设计
# 使用方法: ./scripts/start-production.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
PROJECT_NAME="youpin-sentinel"
PROJECT_PATH="/www/wwwroot/youpin-sentinel"
NODE_ENV="production"

echo -e "${GREEN}🚀 饰品监控助手 - 生产环境启动${NC}"
echo "=================================================="

# 检查当前目录
check_directory() {
    echo -e "${BLUE}📁 检查项目目录...${NC}"
    
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ 错误: 请在项目根目录执行此脚本${NC}"
        echo -e "${YELLOW}当前目录: $(pwd)${NC}"
        echo -e "${YELLOW}期望目录: $PROJECT_PATH${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 项目目录正确${NC}"
}

# 检查环境配置
check_environment() {
    echo -e "${BLUE}⚙️ 检查环境配置...${NC}"
    
    # 检查生产环境配置文件
    if [ ! -f ".env.production" ]; then
        echo -e "${RED}❌ 错误: .env.production 文件不存在${NC}"
        echo -e "${YELLOW}请先运行: ./scripts/deploy-baota.sh${NC}"
        exit 1
    fi
    
    # 检查JWT密钥
    if grep -q "CHANGE-THIS-TO-RANDOM-STRING" .env.production; then
        echo -e "${RED}❌ 错误: JWT密钥未更换${NC}"
        echo -e "${YELLOW}请修改 .env.production 中的 JWT_SECRET${NC}"
        exit 1
    fi
    
    # 检查域名配置
    if grep -q "yourdomain.com" .env.production; then
        echo -e "${YELLOW}⚠️ 警告: 请检查域名配置是否正确${NC}"
    fi
    
    echo -e "${GREEN}✅ 环境配置检查完成${NC}"
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}📦 检查项目依赖...${NC}"
    
    # 检查Node.js版本
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js 版本: $NODE_VERSION${NC}"
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        echo -e "${YELLOW}📦 安装 PM2...${NC}"
        npm install -g pm2
    fi
    
    PM2_VERSION=$(pm2 -v)
    echo -e "${GREEN}✅ PM2 版本: $PM2_VERSION${NC}"
    
    # 检查node_modules
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 安装项目依赖...${NC}"
        npm ci --only=production
    fi
    
    echo -e "${GREEN}✅ 依赖检查完成${NC}"
}

# 检查数据库
check_database() {
    echo -e "${BLUE}🗄️ 检查数据库...${NC}"
    
    # 创建数据目录
    mkdir -p data logs
    chown -R www:www data logs 2>/dev/null || true
    
    # 检查数据库文件
    if [ ! -f "data/production.db" ]; then
        echo -e "${YELLOW}🔧 初始化数据库...${NC}"
        NODE_ENV=production npx prisma generate
        NODE_ENV=production npx prisma db push
        
        # 创建超级管理员
        echo -e "${YELLOW}👤 创建超级管理员...${NC}"
        NODE_ENV=production node scripts/create-super-admin.js
    fi
    
    echo -e "${GREEN}✅ 数据库检查完成${NC}"
}

# 构建应用
build_application() {
    echo -e "${BLUE}🔨 构建生产版本...${NC}"
    
    # 检查是否需要重新构建
    if [ ! -d ".next" ] || [ "package.json" -nt ".next" ]; then
        npm run build
        echo -e "${GREEN}✅ 应用构建完成${NC}"
    else
        echo -e "${YELLOW}⏭️ 跳过构建（已是最新）${NC}"
    fi
}

# 启动PM2进程
start_pm2() {
    echo -e "${BLUE}🚀 启动 PM2 进程...${NC}"
    
    # 停止现有进程
    pm2 stop $PROJECT_NAME 2>/dev/null || true
    pm2 delete $PROJECT_NAME 2>/dev/null || true
    
    # 启动新进程
    pm2 start ecosystem.config.js --env production
    
    # 保存PM2配置
    pm2 save
    
    # 设置开机自启
    pm2 startup 2>/dev/null || true
    
    echo -e "${GREEN}✅ PM2 进程启动完成${NC}"
}

# 验证部署
verify_deployment() {
    echo -e "${BLUE}🔍 验证部署状态...${NC}"
    
    # 等待应用启动
    sleep 5
    
    # 检查PM2状态
    if pm2 list | grep -q "$PROJECT_NAME.*online"; then
        echo -e "${GREEN}✅ PM2 进程运行正常${NC}"
    else
        echo -e "${RED}❌ PM2 进程启动失败${NC}"
        pm2 logs $PROJECT_NAME --lines 10
        exit 1
    fi
    
    # 检查健康接口
    if command -v curl &> /dev/null; then
        echo -e "${BLUE}🏥 检查应用健康状态...${NC}"
        sleep 3
        
        if curl -f -s http://localhost:3000/api/health > /dev/null; then
            echo -e "${GREEN}✅ 应用健康检查通过${NC}"
        else
            echo -e "${RED}❌ 应用健康检查失败${NC}"
            echo -e "${YELLOW}请检查应用日志: pm2 logs $PROJECT_NAME${NC}"
        fi
    fi
}

# 显示启动结果
show_result() {
    echo ""
    echo -e "${GREEN}🎉 生产环境启动完成！${NC}"
    echo "=================================================="
    echo -e "${BLUE}📋 服务信息:${NC}"
    echo -e "  • 项目名称: $PROJECT_NAME"
    echo -e "  • 项目路径: $(pwd)"
    echo -e "  • 运行环境: $NODE_ENV"
    echo -e "  • 本地访问: ${YELLOW}http://localhost:3000${NC}"
    echo ""
    echo -e "${BLUE}🔑 默认管理员账户:${NC}"
    echo -e "  • 邮箱: ${YELLOW}<EMAIL>${NC}"
    echo -e "  • 密码: ${YELLOW}Admin123456${NC}"
    echo -e "  • ${RED}⚠️ 请首次登录后立即修改密码！${NC}"
    echo ""
    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo -e "  • 查看状态: ${YELLOW}pm2 status${NC}"
    echo -e "  • 查看日志: ${YELLOW}pm2 logs $PROJECT_NAME${NC}"
    echo -e "  • 重启应用: ${YELLOW}pm2 restart $PROJECT_NAME${NC}"
    echo -e "  • 停止应用: ${YELLOW}pm2 stop $PROJECT_NAME${NC}"
    echo -e "  • 健康检查: ${YELLOW}./scripts/health-check.sh${NC}"
    echo ""
    echo -e "${GREEN}✅ 准备就绪！请配置宝塔面板反向代理${NC}"
    echo -e "${BLUE}📖 详细配置指南: docs/ALIYUN_DEPLOYMENT.md${NC}"
}

# 主函数
main() {
    check_directory
    check_environment
    check_dependencies
    check_database
    build_application
    start_pm2
    verify_deployment
    show_result
}

# 执行主函数
main "$@"
