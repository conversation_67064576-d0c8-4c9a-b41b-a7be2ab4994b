/**
 * 应用配置管理
 * 统一管理所有配置项，支持环境变量覆盖
 */

// 默认配置
const DEFAULT_CONFIG = {
  // 应用基础配置
  app: {
    name: '饰品监控助手',
    version: '1.0.0',
    description: 'CS:GO 饰品价格监控和会员管理系统',
    port: 3000,
    baseUrl: 'http://localhost:3000',
  },

  // 数据库配置
  database: {
    url: 'file:./data/dev.db',
    logLevel: 'info',
  },

  // JWT配置
  jwt: {
    secret: 'your-jwt-secret-key',
    expiresIn: '24h',
    issuer: 'youpin-sentinel',
  },

  // 缓存配置
  cache: {
    ttlMinutes: 30,
    maxSize: 1000,
    cleanupInterval: 60, // 分钟
  },

  // 调度器配置
  scheduler: {
    intervalMinutes: 30,
    enabled: true,
    timezone: 'Asia/Shanghai',
  },

  // API配置
  api: {
    timeout: 30000,
    retries: 3,
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 最大请求次数
    },
  },

  // 悠悠有品API配置
  youpin: {
    baseUrl: 'https://gw-openapi.youpin898.com',
    appKey: '5255157',
    privateKey: '', // 从环境变量获取
    timeout: 30000,
  },

  // 支付配置
  payment: {
    alipay: {
      appId: '',
      privateKey: '',
      publicKey: '',
      gateway: 'https://openapi.alipay.com/gateway.do',
    },
    wechat: {
      appId: '',
      mchId: '',
      apiKey: '',
      gateway: 'https://api.mch.weixin.qq.com',
    },
  },

  // 日志配置
  logging: {
    level: 'info',
    filePath: './logs',
    maxFiles: 7,
    maxSize: '10m',
  },

  // 安全配置
  security: {
    bcryptRounds: 12,
    sessionTimeout: 24 * 60 * 60 * 1000, // 24小时
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15分钟
  },

  // 业务配置
  business: {
    subscription: {
      plans: {
        monthly: {
          name: '月度会员',
          price: 20,
          duration: 30, // 天
        },
        yearly: {
          name: '年度会员',
          price: 200,
          duration: 365, // 天
        },
      },
    },
    premium: {
      maxPremiumPercentage: 20, // 最大溢价百分比
      targetTemplates: [], // 目标模板列表
    },
  },
};

/**
 * 配置管理类
 */
export class Config {
  static _config = null;

  /**
   * 获取配置
   */
  static get() {
    if (!this._config) {
      this._config = this._loadConfig();
    }
    return this._config;
  }

  /**
   * 获取特定配置项
   */
  static get(path) {
    const config = this.get();
    return this._getNestedValue(config, path);
  }

  /**
   * 加载配置
   * @private
   */
  static _loadConfig() {
    const config = JSON.parse(JSON.stringify(DEFAULT_CONFIG));

    // 从环境变量覆盖配置
    this._overrideFromEnv(config);

    return config;
  }

  /**
   * 从环境变量覆盖配置
   * @private
   */
  static _overrideFromEnv(config) {
    // 应用配置
    if (process.env.PORT) {
      config.app.port = parseInt(process.env.PORT);
    }
    if (process.env.NEXT_PUBLIC_BASE_URL) {
      config.app.baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
    }

    // 数据库配置
    if (process.env.DATABASE_URL) {
      config.database.url = process.env.DATABASE_URL;
    }

    // JWT配置
    if (process.env.JWT_SECRET) {
      config.jwt.secret = process.env.JWT_SECRET;
    }

    // 缓存配置
    if (process.env.CACHE_TTL_MINUTES) {
      config.cache.ttlMinutes = parseInt(process.env.CACHE_TTL_MINUTES);
    }

    // 调度器配置
    if (process.env.SCHEDULER_INTERVAL_MINUTES) {
      config.scheduler.intervalMinutes = parseInt(process.env.SCHEDULER_INTERVAL_MINUTES);
    }

    // 悠悠有品API配置
    if (process.env.NEXT_PUBLIC_YOUPIN_APP_KEY) {
      config.youpin.appKey = process.env.NEXT_PUBLIC_YOUPIN_APP_KEY;
    }
    if (process.env.NEXT_PUBLIC_YOUPIN_PRIVATE_KEY) {
      config.youpin.privateKey = process.env.NEXT_PUBLIC_YOUPIN_PRIVATE_KEY;
    }

    // 支付配置
    if (process.env.ALIPAY_APP_ID) {
      config.payment.alipay.appId = process.env.ALIPAY_APP_ID;
    }
    if (process.env.ALIPAY_PRIVATE_KEY) {
      config.payment.alipay.privateKey = process.env.ALIPAY_PRIVATE_KEY;
    }
    if (process.env.ALIPAY_PUBLIC_KEY) {
      config.payment.alipay.publicKey = process.env.ALIPAY_PUBLIC_KEY;
    }

    // 微信支付配置
    if (process.env.WECHAT_APP_ID) {
      config.payment.wechat.appId = process.env.WECHAT_APP_ID;
    }
    if (process.env.WECHAT_MCH_ID) {
      config.payment.wechat.mchId = process.env.WECHAT_MCH_ID;
    }
    if (process.env.WECHAT_API_KEY) {
      config.payment.wechat.apiKey = process.env.WECHAT_API_KEY;
    }

    // 日志配置
    if (process.env.LOG_LEVEL) {
      config.logging.level = process.env.LOG_LEVEL;
    }
    if (process.env.LOG_FILE_PATH) {
      config.logging.filePath = process.env.LOG_FILE_PATH;
    }
  }

  /**
   * 获取嵌套配置值
   * @private
   */
  static _getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 验证配置
   */
  static validate() {
    const config = this.get();
    const errors = [];

    // 验证必需的配置项
    if (!config.jwt.secret || config.jwt.secret === 'your-jwt-secret-key') {
      errors.push('JWT_SECRET 必须设置为强随机密钥');
    }

    if (!config.database.url) {
      errors.push('DATABASE_URL 必须设置');
    }

    if (process.env.NODE_ENV === 'production') {
      if (config.app.baseUrl.includes('localhost')) {
        errors.push('生产环境必须设置正确的 NEXT_PUBLIC_BASE_URL');
      }
    }

    if (errors.length > 0) {
      throw new Error(`配置验证失败:\n${errors.join('\n')}`);
    }

    return true;
  }

  /**
   * 获取环境信息
   */
  static getEnvironment() {
    return {
      nodeEnv: process.env.NODE_ENV || 'development',
      isProduction: process.env.NODE_ENV === 'production',
      isDevelopment: process.env.NODE_ENV !== 'production',
      platform: process.platform,
      nodeVersion: process.version,
    };
  }
}

// 导出常用配置的快捷访问
export const APP_CONFIG = Config.get().app;
export const DB_CONFIG = Config.get().database;
export const JWT_CONFIG = Config.get().jwt;
export const CACHE_CONFIG = Config.get().cache;
export const SCHEDULER_CONFIG = Config.get().scheduler;
export const API_CONFIG = Config.get().api;
export const YOUPIN_CONFIG = Config.get().youpin;
export const PAYMENT_CONFIG = Config.get().payment;
export const LOGGING_CONFIG = Config.get().logging;
export const SECURITY_CONFIG = Config.get().security;
export const BUSINESS_CONFIG = Config.get().business;

// 默认导出
export default Config;
