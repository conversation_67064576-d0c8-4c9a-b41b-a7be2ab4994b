// 饰品基础数据
export const ID_DATA = [
 {
    "id": 1785,
    "name": "爪子刀（★） | 多普勒 (崭新出厂)",
    "hashName": "★ Karambit | Doppler (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 10,
    "weaponName": "爪子刀",
    "weaponHashName": "weapon_knife_karambit",
    "updateTime": "2022-01-08 01:33:19"
  },
  {
    "id": 754,
    "name": "M9 刺刀（★） | 多普勒 (崭新出厂)",
    "hashName": "★ M9 Bayonet | Doppler (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 8,
    "weaponName": "M9刺刀",
    "weaponHashName": "weapon_knife_m9_bayonet",
    "updateTime": "2022-01-08 01:56:38"
  },
  {
    "id": 43574,
    "name": "折叠刀（★） | 多普勒 (崭新出厂)",
    "hashName": "★ Flip Knife | Doppler (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 5,
    "weaponName": "折叠刀",
    "weaponHashName": "weapon_knife_flip",
    "updateTime": "2022-01-08 01:51:55"
  },
  {
    "id": 45985,
    "name": "蝴蝶刀（★） | 多普勒 (崭新出厂)",
    "hashName": "★ Butterfly Knife | Doppler (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 3,
    "weaponName": "蝴蝶刀",
    "weaponHashName": "weapon_knife_butterfly",
    "updateTime": "2022-01-08 01:23:31"
  },
  {
    "id": 43529,
    "name": "爪子刀（★） | 伽玛多普勒 (崭新出厂)",
    "hashName": "★ Karambit | Gamma Doppler (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 10,
    "weaponName": "爪子刀",
    "weaponHashName": "weapon_knife_karambit",
    "updateTime": "2022-01-08 00:30:55"
  },
  {
    "id": 47847,
    "name": "M9 刺刀（★） | 伽玛多普勒 (崭新出厂)",
    "hashName": "★ M9 Bayonet | Gamma Doppler (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 8,
    "weaponName": "M9刺刀",
    "weaponHashName": "weapon_knife_m9_bayonet",
    "updateTime": "2022-01-08 01:18:24"
  },
  {
    "id": 62272,
    "name": "蝴蝶刀（★） | 伽玛多普勒 (崭新出厂)",
    "hashName": "★ Butterfly Knife | Gamma Doppler (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 3,
    "weaponName": "蝴蝶刀",
    "weaponHashName": "weapon_knife_butterfly",
    "updateTime": "2022-01-08 01:53:23"
  },
  {
    "id": 46429,
    "name": "爪子刀（★） | 渐变大理石 (崭新出厂)",
    "hashName": "★ Karambit | Marble Fade (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 10,
    "weaponName": "爪子刀",
    "weaponHashName": "weapon_knife_karambit",
    "updateTime": "2022-01-07 23:36:39"
  },
  {
    "id": 34967,
    "name": "蝴蝶刀（★） | 渐变大理石 (崭新出厂)",
    "hashName": "★ Butterfly Knife | Marble Fade (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 3,
    "weaponName": "蝴蝶刀",
    "weaponHashName": "weapon_knife_butterfly",
    "updateTime": "2022-01-08 01:40:14"
  },
  {
    "id": 34973,
    "name": "M9 刺刀（★） | 渐变大理石 (崭新出厂)",
    "hashName": "★ M9 Bayonet | Marble Fade (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 8,
    "weaponName": "M9刺刀",
    "weaponHashName": "weapon_knife_m9_bayonet",
    "updateTime": "2022-01-08 00:47:13"
  },
  {
    "id": 58148,
    "name": "专业手套（★） | 渐变之色 (崭新出厂)",
    "hashName": "★ Specialist Gloves | Fade (Factory New)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-03 23:41:28"
  },
  {
    "id": 44668,
    "name": "专业手套（★） | 渐变之色 (略有磨损)",
    "hashName": "★ Specialist Gloves | Fade (Minimal Wear)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-08 01:37:39"
  },
  {
    "id": 2130,
    "name": "专业手套（★） | 渐变之色 (久经沙场)",
    "hashName": "★ Specialist Gloves | Fade (Field-Tested)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-08 01:02:57"
  },
  {
    "id": 55030,
    "name": "专业手套（★） | 渐变之色 (破损不堪)",
    "hashName": "★ Specialist Gloves | Fade (Well-Worn)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-07 22:29:48"
  },
  {
    "id": 49114,
    "name": "专业手套（★） | 渐变之色 (战痕累累)",
    "hashName": "★ Specialist Gloves | Fade (Battle-Scarred)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-07 20:49:17"
  },
  {
    "id": 58331,
    "name": "专业手套（★） | 深红和服 (崭新出厂)",
    "hashName": "★ Specialist Gloves | Crimson Kimono (Factory New)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2021-11-30 23:30:19"
  },
  {
    "id": 49118,
    "name": "专业手套（★） | 深红和服 (略有磨损)",
    "hashName": "★ Specialist Gloves | Crimson Kimono (Minimal Wear)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-07 17:29:03"
  },
  {
    "id": 46476,
    "name": "专业手套（★） | 深红和服 (久经沙场)",
    "hashName": "★ Specialist Gloves | Crimson Kimono (Field-Tested)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-08 00:04:47"
  },
  {
    "id": 49793,
    "name": "专业手套（★） | 深红和服 (破损不堪)",
    "hashName": "★ Specialist Gloves | Crimson Kimono (Well-Worn)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-07 23:34:59"
  },
  {
    "id": 47301,
    "name": "专业手套（★） | 深红和服 (战痕累累)",
    "hashName": "★ Specialist Gloves | Crimson Kimono (Battle-Scarred)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 201,
    "weaponName": "专业手套",
    "weaponHashName": "Specialist Gloves",
    "updateTime": "2022-01-08 00:00:56"
  },
  {
    "id": 57526,
    "name": "运动手套（★） | 夜行衣 (崭新出厂)",
    "hashName": "★ Sport Gloves | Nocts (Factory New)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2021-12-15 17:20:33"
  },
  {
    "id": 53559,
    "name": "运动手套（★） | 夜行衣 (略有磨损)",
    "hashName": "★ Sport Gloves | Nocts (Minimal Wear)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-07 18:31:30"
  },
  {
    "id": 51135,
    "name": "运动手套（★） | 夜行衣 (久经沙场)",
    "hashName": "★ Sport Gloves | Nocts (Field-Tested)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 01:46:41"
  },
  {
    "id": 47446,
    "name": "运动手套（★） | 夜行衣 (破损不堪)",
    "hashName": "★ Sport Gloves | Nocts (Well-Worn)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 01:33:12"
  },
  {
    "id": 53062,
    "name": "运动手套（★） | 夜行衣 (战痕累累)",
    "hashName": "★ Sport Gloves | Nocts (Battle-Scarred)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 00:52:20"
  },
  {
    "id": 54795,
    "name": "运动手套（★） | 双栖 (崭新出厂)",
    "hashName": "★ Sport Gloves | Amphibious (Factory New)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2021-12-28 13:54:07"
  },
  {
    "id": 47079,
    "name": "运动手套（★） | 双栖 (略有磨损)",
    "hashName": "★ Sport Gloves | Amphibious (Minimal Wear)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-07 22:26:57"
  },
  {
    "id": 741,
    "name": "运动手套（★） | 双栖 (久经沙场)",
    "hashName": "★ Sport Gloves | Amphibious (Field-Tested)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 01:36:21"
  },
  {
    "id": 48781,
    "name": "运动手套（★） | 双栖 (破损不堪)",
    "hashName": "★ Sport Gloves | Amphibious (Well-Worn)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 01:55:44"
  },
  {
    "id": 47368,
    "name": "运动手套（★） | 双栖 (战痕累累)",
    "hashName": "★ Sport Gloves | Amphibious (Battle-Scarred)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 01:44:53"
  },
  {
    "id": 53511,
    "name": "运动手套（★） | 迈阿密风云 (崭新出厂)",
    "hashName": "★ Sport Gloves | Vice (Factory New)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-07 13:38:57"
  },
  {
    "id": 47849,
    "name": "运动手套（★） | 迈阿密风云 (略有磨损)",
    "hashName": "★ Sport Gloves | Vice (Minimal Wear)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 01:47:11"
  },
  {
    "id": 1979,
    "name": "运动手套（★） | 迈阿密风云 (久经沙场)",
    "hashName": "★ Sport Gloves | Vice (Field-Tested)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 01:59:23"
  },
  {
    "id": 46478,
    "name": "运动手套（★） | 迈阿密风云 (破损不堪)",
    "hashName": "★ Sport Gloves | Vice (Well-Worn)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-07 23:14:53"
  },
  {
    "id": 45280,
    "name": "运动手套（★） | 迈阿密风云 (战痕累累)",
    "hashName": "★ Sport Gloves | Vice (Battle-Scarred)",
    "typeId": 60,
    "typeName": "手套",
    "typeHashName": "Type_Hands",
    "weaponId": 220,
    "weaponName": "运动手套",
    "weaponHashName": "Sport Gloves",
    "updateTime": "2022-01-08 00:23:56"
  },
  {
    "id": 798,
    "name": "AK-47 | 表面淬火 (崭新出厂)",
    "hashName": "AK-47 | Case Hardened (Factory New)",
    "typeId": 32,
    "typeName": "步枪",
    "typeHashName": "CSGO_Type_Rifle",
    "weaponId": 36,
    "weaponName": "AK-47",
    "weaponHashName": "weapon_ak47",
    "updateTime": "2022-01-08 00:38:17"
  },
  {
    "id": 43804,
    "name": "AK-47 | 表面淬火 (略有磨损)",
    "hashName": "AK-47 | Case Hardened (Minimal Wear)",
    "typeId": 32,
    "typeName": "步枪",
    "typeHashName": "CSGO_Type_Rifle",
    "weaponId": 36,
    "weaponName": "AK-47",
    "weaponHashName": "weapon_ak47",
    "updateTime": "2022-01-08 00:44:07"
  },
  {
    "id": 494,
    "name": "AK-47 | 表面淬火 (久经沙场)",
    "hashName": "AK-47 | Case Hardened (Field-Tested)",
    "typeId": 32,
    "typeName": "步枪",
    "typeHashName": "CSGO_Type_Rifle",
    "weaponId": 36,
    "weaponName": "AK-47",
    "weaponHashName": "weapon_ak47",
    "updateTime": "2022-01-08 01:53:24"
  },
  {
    "id": 1021,
    "name": "AK-47 | 表面淬火 (破损不堪)",
    "hashName": "AK-47 | Case Hardened (Well-Worn)",
    "typeId": 32,
    "typeName": "步枪",
    "typeHashName": "CSGO_Type_Rifle",
    "weaponId": 36,
    "weaponName": "AK-47",
    "weaponHashName": "weapon_ak47",
    "updateTime": "2022-01-07 21:29:37"
  },
  {
    "id": 47636,
    "name": "AK-47 | 表面淬火 (战痕累累)",
    "hashName": "AK-47 | Case Hardened (Battle-Scarred)",
    "typeId": 32,
    "typeName": "步枪",
    "typeHashName": "CSGO_Type_Rifle",
    "weaponId": 36,
    "weaponName": "AK-47",
    "weaponHashName": "weapon_ak47",
    "updateTime": "2022-01-07 21:02:36"
  },
  {
    "id": 43950,
    "name": "M4A1 消音型 | 伊卡洛斯殒落 (崭新出厂)",
    "hashName": "M4A1-S | Icarus Fell (Factory New)",
    "typeId": 32,
    "typeName": "步枪",
    "typeHashName": "CSGO_Type_Rifle",
    "weaponId": 39,
    "weaponName": "M4A1消音型",
    "weaponHashName": "weapon_m4a1_silencer",
    "updateTime": "2022-01-08 01:51:00"
  },
  {
    "id": 45912,
    "name": "短剑（★） | 多普勒 (崭新出厂)",
    "hashName": "★ Stiletto Knife | Doppler (Factory New)",
    "typeId": 1,
    "typeName": "匕首",
    "typeHashName": "CSGO_Type_Knife",
    "weaponId": 12,
    "weaponName": "短剑",
    "weaponHashName": "weapon_knife_stiletto",
    "updateTime": "2022-01-08 00:57:37"
  },
  {
    "id": 61735,
    "name": "格洛克 18 型 | 伽玛多普勒 (崭新出厂)",
    "hashName": "Glock-18 | Gamma Doppler (Factory New)",
    "typeId": 21,
    "typeName": "手枪",
    "typeHashName": "CSGO_Type_Pistol",
    "weaponId": 24,
    "weaponName": "格洛克18型",
    "weaponHashName": "weapon_glock",
    "updateTime": "2022-01-08 01:33:59"
  },
];
