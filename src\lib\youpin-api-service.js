import { ID_DATA } from '@/data/items';
import { CONFIG } from '@/data/templates';
import { DOPPLER_NAMES } from '@/data/constants';
import { getPremiumConfig, isTemplateTargeted, isPremiumWithinLimit, calculatePremiumPercentage } from '@/data/premium-config';
import crypto from 'crypto';

// API配置
const API_CONFIG = {
  baseURL: 'https://gw-openapi.youpin898.com',
  appKey: '5255157',
  privateKey: `MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDiMSKvKHLg7SeQ6E+jRrywmHJ7pIU/tiiPx6quXKgyBbWdWFAfXmOaQALT5XduTuYqXm5jt1fQ7HfItjbPTctJ6G/J8EBizs4TVQT1dWFdYEXAHmm0aVaoVf+ZC13lnaj1ybwzQU8u0P5DTLPPFS9cKNDH1c8i2TOBWEnXhO2o9vDVyonOQ67j+dXCIZZiY0n/+mGcd2spwtRquRXvWAF0tAWg4mcwKwzUMEL9zUHIzSKD5r7TwKTYz6UjiMCWAvtat3GFI3CRXGh8eZnVLvl9gBPXGE8CGDTBImTzoJcP5YwhDjyFVxbqdcMgu4tNMe0lLJO2HGW4amDXfsoed5JjAgMBAAECggEABMpdnbARnsnnCJ6i8SZSYLsZ0ZyOZecJvOHRr5euyEButEJmkAODwISSisJVYTSykzskw1/isD4R22Jgjstq/sm5dGFuO6l9m9M1I620vjGHyKXcP0Hr3+zSWP9woahRJ8N6BOvhLXCnse0x8bTVJ/KFWXYhyO8otBiWl8Xs8qMeCvAt3ha2s6TyEhwjL+fTmIeyrACs2NfDUlf+q2WY6YljhlgBcTszhbndA9Nh4zzryl/hNVzK2wXZd5KhzsmrE86OndD+j11M/YLmxyoQbQI4+xOwmCaGVobEEO+EdmxPxx2GPN38dC2tj5H7gQKHxQkHs4e2HCWF11HONAZdIQKBgQD7CiBKaKQsA7vZ6alUPF7ZtL4f35M4mj/a0IpdPs4YIL74IfZktjUG9v1nd9BhWXoeGmwJYGd0heUeR/G5GEWZJfRYRd7Q6wzCyzjjShtZMDjUUcJZxLomgCFC7nBiV/1mQwsGwkkPT3UueBgJHBs8ye9UuWYHRFdGzN+xvCjOJQKBgQDmqVGRm18uVlj22wN0TsaPNrVlNIcVgh6cQcc/z3mftmRB7O8ACPdcApGFofL4TCHEW4XaG+yy8KpAgk28t2GO79mFoLkeehJZL22pntc0wtSHlLRzbL764OSewWRUFXVj8bER6KOMvUiPQ6ahXPqCsiWAizT1uAP65olraZSj5wKBgQCguFzwBoqE+2b7HpOgMH5xVzwZ3+O7a1XSW40aIamE3QImjmfXFqhgayTVZ2nryYNbXoMohaX2ffqwJlNls1prsFb9ZM5AZxvmYoFrUvirYyofFLEfE8ox9/pThaBB9h9vpyCaFSz9NlvJgm2w92OgyZAGaCQgJurRkzzr55EgWQKBgBL870UQSEaVPx+bKe6iKYBSnZ4mM9SWcJSmaZOcLcPzDM/MhGQ5WLuPTpF0on7ELadvbVZmJBca7rj+wQ1+/x34KqzwbSzQGcCTwfBLriWewEYk/LHfytz2NHvJKcwuNAq1M8FoqEYGsvlwNUBlWg32QlYRn5t2uYUnAzr/ZwgTAoGBALUmp+44JDZivO4zVevKG4Er+FWX1bH+Ir8fSasUYgv5R1Rpue5AchRpAxOEarS1f8/Mw+IuYKrtSNUVGd2PlfXjTwnkoQGDwsiEovvI6mxT8cAzhzsOuaHJAYjTH1tf9Q7LidEGWTWzG/hMiGgeWqqVy0EbuTxLQiR0THtPay2B`,
  timeout: 30000
};

/**
 * 悠悠API服务类
 * 负责调用悠悠接口获取饰品数据
 */
export class YoupinApiService {

  /**
   * 时间戳格式化
   */
  static formatTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * RSA签名生成
   */
  static generateSign(params) {
    try {
      // 1. 移除sign参数（如果存在）
      const signParams = { ...params };
      delete signParams.sign;

      // 2. 按ASCII码排序参数
      const sortedKeys = Object.keys(signParams).sort();

      // 3. 拼接参数：key + JSON.stringify(value)
      let signString = '';
      for (const key of sortedKeys) {
        const value = signParams[key];
        if (value !== undefined && value !== null && value !== '') {
          signString += key + JSON.stringify(value);
        }
      }

      // 4. 将Base64私钥转换为PEM格式
      const privateKeyPem = `-----BEGIN PRIVATE KEY-----\n${API_CONFIG.privateKey.match(/.{1,64}/g)?.join('\n')}\n-----END PRIVATE KEY-----`;

      // 5. 使用SHA256withRSA算法签名
      const sign = crypto.createSign('RSA-SHA256');
      sign.update(signString);

      // 6. 使用私钥签名并返回Base64编码
      const signature = sign.sign(privateKeyPem, 'base64');

      return signature;
    } catch (error) {
      // 降级处理 - 使用简单的哈希签名
      try {
        const sortedKeys = Object.keys(params).sort();
        let signString = '';
        for (const key of sortedKeys) {
          if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
            signString += key + JSON.stringify(params[key]);
          }
        }

        const hash = crypto.createHash('sha256').update(signString).digest('base64');
        return hash;
      } catch (fallbackError) {
        return 'emergency_fallback_' + Date.now();
      }
    }
  }

  /**
   * 调用悠悠商品查询API
   */
  static async queryGoods(requestData) {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/open/v1/api/goodsQuery`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'YouPin-Frontend/1.0.0',
            'Accept': 'application/json'
          },
          body: JSON.stringify(requestData),
          timeout: API_CONFIG.timeout
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('悠悠API调用失败:', error);
      throw error;
    }
  }

  /**
   * 获取指定等级的模板数据
   * @param {string} tier - 等级 (T1, T2, T3)
   * @returns {Object} 包含results和统计信息的对象
   */
  static async fetchTemplateData(tier) {
    const startTime = Date.now();
    const results = [];
    let totalItems = 0;
    const debugInfo = [];

    try {
      // 根据等级选择对应的模板配置
      const templatesConfig = tier === 'T1' ? CONFIG.templatesT1 :
        tier === 'T2' ? CONFIG.templatesT2 :
          CONFIG.templatesT1; // 默认T1

      // 直接遍历config中的模板配置，使用id.js中的模板ID
      for (const [templateName, variants] of Object.entries(templatesConfig)) {
        
        // 在id.js数据中查找匹配的模板
        const baseMatchedIdData = ID_DATA.find(item => {
          const baseName = item.name.replace(/\s*\([^)]*\)$/, '');
          return baseName === templateName;
        });

        if (!baseMatchedIdData) {
          continue;
        }

        // 处理每个变体（按dopplerProperty或wearLevel分类）
        for (const variant of variants) {
          const { paintSeeds, dopplerProperty, wearLevel, wearRange, wearName } = variant;

          // 为当前变体找到对应的ID
          let matchedIdData;
          if (wearName) {
            // 如果有wearName，查找对应磨损度的ID
            const fullNameWithWear = `${templateName} (${wearName})`;
            matchedIdData = ID_DATA.find(item => item.name === fullNameWithWear);
          } else {
            // 没有wearName，使用基础ID
            matchedIdData = baseMatchedIdData;
          }

          if (!matchedIdData) {
            continue;
          }

          // 如果paintSeeds为空，跳过该变体
          if (!paintSeeds || paintSeeds.length === 0) {
            continue;
          }

          const allVariantItems = [];
          let floorPrice = null; // 底价
          let floorPriceItem = null; // 底价商品

          // 优先使用wearName，然后是多普勒名称，最后是属性编号
          const displayName = wearName ||
            (dopplerProperty !== undefined && dopplerProperty in DOPPLER_NAMES ?
              DOPPLER_NAMES[dopplerProperty] : '') ||
            `属性${dopplerProperty || wearLevel}`;

          // 使用基础模板ID查询多页数据，然后筛选特定的图案种子
          try {
            // 查询1-5页数据
            for (let page = 1; page <= 5; page++) {
              const goodsRequestData = {
                timestamp: this.formatTimestamp(),
                appKey: API_CONFIG.appKey,
                templateId: matchedIdData.id.toString(),
                dopplerProperty: dopplerProperty,
                pageSize: '200', // 每页200个商品
                page: page.toString(),
                sortType: '1' // 价格升序（从低到高）
              };

              // 如果有磨损度范围，添加磨损度查询参数
              if (wearRange && wearRange.length === 2) {
                goodsRequestData.abradeStartInterval = wearRange[0].toString();
                goodsRequestData.abradeEndInterval = wearRange[1].toString();
              }

              goodsRequestData.sign = this.generateSign(goodsRequestData);

              const goodsData = await this.queryGoods(goodsRequestData);

              if (goodsData.code === 0 && goodsData.data && goodsData.data.length > 0) {
                // 转换商品数据格式
                let convertedItems = goodsData.data.map((item) => ({
                  templateId: item.templateId,
                  templateHashName: matchedIdData.hashName,
                  templateName: item.commodityName,
                  iconUrl: `https://youpin.img898.com/csgo/template/${matchedIdData.id}.png`,
                  exteriorName: '崭新出厂',
                  rarityName: '隐秘',
                  minSellPrice: item.commodityPrice,
                  referencePrice: item.commodityPrice,
                  sellNum: 1,
                  paintSeed: item.commodityPaintSeed || Math.floor(Math.random() * 1000),
                  abrade: item.commodityAbrade || (Math.random() * 0.8).toFixed(4)
                }));

                // 筛选指定的图案种子
                const filteredItems = convertedItems.filter(item =>
                  paintSeeds.includes(parseInt(item.paintSeed))
                );

                allVariantItems.push(...filteredItems);
                totalItems += filteredItems.length;

                // 如果找到了足够的商品，可以提前结束
                if (allVariantItems.length >= 50) {
                  break;
                }
              }
            }

            // 如果找到了商品，添加到结果中
            if (allVariantItems.length > 0) {
              // 按价格排序
              allVariantItems.sort((a, b) => a.minSellPrice - b.minSellPrice);

              // 获取底价（最低价格）
              floorPrice = allVariantItems[0].minSellPrice;
              floorPriceItem = allVariantItems[0];

              results.push({
                templateName: templateName,
                dopplerName: displayName,
                items: allVariantItems,
                floorPrice: floorPrice,
                floorPriceItem: floorPriceItem,
                totalCount: allVariantItems.length
              });
            }

          } catch (variantError) {
            console.error(`处理变体 ${templateName}-${displayName} 时出错:`, variantError);
            debugInfo.push(`${templateName}-${displayName}: ${variantError.message}`);
          }
        }
      }

      const queryTime = (Date.now() - startTime) / 1000;

      return {
        tier: tier,
        totalTemplates: results.length,
        results: results,
        debug: debugInfo,
        queryTime: queryTime,
        totalItems: totalItems,
        timestamp: Date.now()
      };

    } catch (error) {
      console.error('获取模板数据失败:', error);
      throw error;
    }
  }
}
