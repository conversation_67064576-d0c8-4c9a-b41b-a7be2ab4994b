#!/bin/bash

# 饰品监控助手 - 健康检查脚本
# 用于监控应用状态和系统健康

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
BASE_URL=${1:-"http://localhost:3000"}
TIMEOUT=10

echo -e "${GREEN}🏥 饰品监控助手健康检查${NC}"
echo -e "${BLUE}检查地址: $BASE_URL${NC}"
echo "=================================================="

# 检查函数
check_service() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "🔍 检查 $name ... "
    
    if command -v curl >/dev/null 2>&1; then
        response=$(curl -s -w "%{http_code}" -o /tmp/health_response --connect-timeout $TIMEOUT "$url" 2>/dev/null || echo "000")
    else
        echo -e "${YELLOW}跳过 (curl 未安装)${NC}"
        return 0
    fi
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 正常${NC}"
        return 0
    else
        echo -e "${RED}❌ 异常 (状态码: $response)${NC}"
        return 1
    fi
}

# 检查JSON响应
check_json_api() {
    local name=$1
    local url=$2
    local expected_field=$3
    
    echo -n "🔍 检查 $name ... "
    
    if command -v curl >/dev/null 2>&1; then
        response=$(curl -s --connect-timeout $TIMEOUT "$url" 2>/dev/null || echo "{}")
        
        if command -v jq >/dev/null 2>&1; then
            field_value=$(echo "$response" | jq -r ".$expected_field" 2>/dev/null || echo "null")
            if [ "$field_value" != "null" ] && [ "$field_value" != "" ]; then
                echo -e "${GREEN}✅ 正常 ($field_value)${NC}"
                return 0
            else
                echo -e "${RED}❌ 异常 (字段 $expected_field 不存在)${NC}"
                return 1
            fi
        else
            if echo "$response" | grep -q "$expected_field"; then
                echo -e "${GREEN}✅ 正常${NC}"
                return 0
            else
                echo -e "${RED}❌ 异常 (响应格式错误)${NC}"
                return 1
            fi
        fi
    else
        echo -e "${YELLOW}跳过 (curl 未安装)${NC}"
        return 0
    fi
}

# 检查PM2进程
check_pm2() {
    echo -n "🔍 检查 PM2 进程 ... "
    
    if command -v pm2 >/dev/null 2>&1; then
        if pm2 list | grep -q "youpin-sentinel.*online"; then
            echo -e "${GREEN}✅ 正常${NC}"
            return 0
        else
            echo -e "${RED}❌ 异常 (进程未运行)${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}跳过 (PM2 未安装)${NC}"
        return 0
    fi
}

# 检查磁盘空间
check_disk_space() {
    echo -n "🔍 检查磁盘空间 ... "
    
    if command -v df >/dev/null 2>&1; then
        usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
        if [ "$usage" -lt 90 ]; then
            echo -e "${GREEN}✅ 正常 (${usage}% 已使用)${NC}"
            return 0
        else
            echo -e "${RED}❌ 警告 (${usage}% 已使用)${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}跳过 (df 命令不可用)${NC}"
        return 0
    fi
}

# 检查内存使用
check_memory() {
    echo -n "🔍 检查内存使用 ... "
    
    if command -v free >/dev/null 2>&1; then
        usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
        if [ "$usage" -lt 90 ]; then
            echo -e "${GREEN}✅ 正常 (${usage}% 已使用)${NC}"
            return 0
        else
            echo -e "${RED}❌ 警告 (${usage}% 已使用)${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}跳过 (free 命令不可用)${NC}"
        return 0
    fi
}

# 主要检查项目
main_checks() {
    local failed=0
    
    echo -e "${BLUE}📋 核心服务检查${NC}"
    echo "----------------------------------------"
    
    # 检查健康接口
    check_json_api "健康检查接口" "$BASE_URL/api/health" "status" || ((failed++))
    
    # 检查认证接口
    check_service "认证接口" "$BASE_URL/api/auth/login" 405 || ((failed++)) # POST方法，GET返回405
    
    # 检查模板接口
    check_json_api "模板数据接口" "$BASE_URL/api/templates?tier=T1" "code" || ((failed++))
    
    # 检查缓存接口
    check_json_api "缓存状态接口" "$BASE_URL/api/cache?action=status" "code" || ((failed++))
    
    echo ""
    echo -e "${BLUE}📋 系统资源检查${NC}"
    echo "----------------------------------------"
    
    # 检查PM2进程
    check_pm2 || ((failed++))
    
    # 检查磁盘空间
    check_disk_space || ((failed++))
    
    # 检查内存使用
    check_memory || ((failed++))
    
    return $failed
}

# 详细检查
detailed_checks() {
    echo ""
    echo -e "${BLUE}📋 详细健康信息${NC}"
    echo "----------------------------------------"
    
    # 获取健康检查详细信息
    if command -v curl >/dev/null 2>&1; then
        health_data=$(curl -s --connect-timeout $TIMEOUT "$BASE_URL/api/health" 2>/dev/null || echo "{}")
        
        if command -v jq >/dev/null 2>&1; then
            echo -e "${BLUE}🗄️ 数据库状态:${NC} $(echo "$health_data" | jq -r '.database // "未知"')"
            echo -e "${BLUE}👥 总用户数:${NC} $(echo "$health_data" | jq -r '.stats.totalUsers // "未知"')"
            echo -e "${BLUE}📊 活跃订阅:${NC} $(echo "$health_data" | jq -r '.stats.activeSubscriptions // "未知"')"
            echo -e "${BLUE}🕐 检查时间:${NC} $(echo "$health_data" | jq -r '.timestamp // "未知"')"
        else
            echo -e "${BLUE}📄 健康检查响应:${NC}"
            echo "$health_data" | head -5
        fi
    fi
    
    # PM2详细信息
    if command -v pm2 >/dev/null 2>&1; then
        echo ""
        echo -e "${BLUE}🔧 PM2 进程状态:${NC}"
        pm2 list | grep youpin-sentinel || echo "未找到 youpin-sentinel 进程"
    fi
}

# 生成报告
generate_report() {
    local failed=$1
    
    echo ""
    echo "=================================================="
    
    if [ $failed -eq 0 ]; then
        echo -e "${GREEN}🎉 所有检查通过！系统运行正常${NC}"
        exit 0
    else
        echo -e "${RED}⚠️ 发现 $failed 个问题，请检查上述错误信息${NC}"
        echo ""
        echo -e "${YELLOW}💡 常见解决方案:${NC}"
        echo "  • 应用未启动: pm2 start youpin-sentinel"
        echo "  • 端口被占用: netstat -tlnp | grep 3000"
        echo "  • 内存不足: pm2 restart youpin-sentinel"
        echo "  • 磁盘空间不足: 清理日志文件和临时文件"
        exit 1
    fi
}

# 主函数
main() {
    # 执行主要检查
    main_checks
    failed=$?
    
    # 显示详细信息
    detailed_checks
    
    # 生成报告
    generate_report $failed
}

# 执行主函数
main "$@"
